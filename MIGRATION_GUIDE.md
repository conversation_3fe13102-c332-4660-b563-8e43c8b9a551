# Frontend Migration Guide: Supabase to API-Based Authentication

This document outlines the migration from Supabase client-side authentication to API-based authentication using Axios.

## Overview

The frontend has been migrated to remove direct Supabase dependencies and instead communicate with the backend API for all authentication and user management operations.

## Changes Made

### 1. Dependencies
- **Removed**: `@supabase/supabase-js`
- **Added**: `axios`

### 2. New Service Layer

#### API Client (`src/services/api-client.ts`)
- Centralized Axios configuration
- Request/response interceptors for authentication
- Automatic token refresh handling
- Token management utilities

#### Auth Service (`src/services/auth-service.ts`)
- Sign up, sign in, sign out functionality
- Profile management
- Password reset and change
- Session management

#### User Service (`src/services/user-service.ts`)
- User profile operations
- User existence checks
- Admin user management (if applicable)

### 3. Updated Components

#### Auth Store (`src/stores/auth-store.ts`)
- Removed Supabase session dependency
- Updated to use new API-based auth service
- Token-based authentication state management

#### Profile Page (`src/components/dashboard/ProfilePage.tsx`)
- Updated user data structure (removed `user_metadata`)
- Direct property access (`user.full_name` instead of `user.user_metadata.full_name`)

#### App Component (`src/App.tsx`)
- Removed session dependency
- Updated to use token manager for authentication checks

### 4. Environment Configuration

#### Development (`.env.development`)
```
VITE_API_BASE_URL=http://localhost:3001
VITE_MASTRA_API_BASE_URL=http://localhost:4111
```

#### Production (`.env.production`)
```
VITE_API_BASE_URL=https://your-api-domain.com
VITE_MASTRA_API_BASE_URL=https://your-mastra-domain.com
```

## API Endpoints Expected

The frontend now expects the following API endpoints to be available:

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Token refresh
- `GET /api/auth/profile` - Get current user profile
- `POST /api/auth/reset-password` - Password reset
- `POST /api/auth/change-password` - Change password

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `DELETE /api/users/account` - Delete user account
- `GET /api/users/exists` - Check if user exists
- `GET /api/users` - Get all users (admin)
- `GET /api/users/:id` - Get user by ID (admin)
- `DELETE /api/users/:id` - Delete user by ID (admin)

## Token Management

### Storage
- Access tokens stored in `localStorage` as `deepledger_access_token`
- Refresh tokens stored in `localStorage` as `deepledger_refresh_token`

### Automatic Refresh
- Interceptors automatically handle 401 responses
- Attempts token refresh before redirecting to login
- Clears tokens and redirects on refresh failure

## User Data Structure

### Before (Supabase)
```typescript
{
  id: string;
  email: string;
  user_metadata: {
    full_name?: string;
    avatar_url?: string;
  };
  // ... other Supabase fields
}
```

### After (API-based)
```typescript
{
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}
```

## Migration Benefits

1. **Centralized Authentication**: All auth logic now handled by the backend
2. **Better Security**: No client-side exposure of sensitive operations
3. **Consistent API**: Unified interface for all data operations
4. **Token Management**: Automatic handling of token refresh and expiration
5. **Error Handling**: Centralized error handling with proper user feedback

## Testing the Migration

1. Start the API server: `cd api && npm run dev`
2. Start the frontend: `cd frontend && npm run dev`
3. Test authentication flows:
   - Sign up new user
   - Sign in existing user
   - Update profile
   - Sign out
   - Token refresh (wait for token expiration or manually test)

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure API server has proper CORS configuration
2. **Token Issues**: Check that API endpoints return tokens in expected format
3. **Environment Variables**: Verify `.env` files are properly configured
4. **API Endpoints**: Ensure all expected endpoints are implemented in the backend

### Debug Tips

1. Check browser console for network errors
2. Verify API responses match expected format
3. Check localStorage for token storage
4. Monitor network tab for failed requests
