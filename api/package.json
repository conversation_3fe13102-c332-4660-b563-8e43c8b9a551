{"name": "deepledger-api", "version": "1.0.0", "description": "DeepLedger API Backend", "main": "dist/app.js", "scripts": {"build": "tsc && tsc-alias", "start": "node dist/app.js", "dev": "ts-node-dev --respawn --transpile-only src/app.ts", "dev:debug": "ts-node-dev --respawn --transpile-only --inspect=9229 src/app.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["api", "backend", "deepledger", "supabase"], "author": "DeepLedger Team", "license": "MIT", "dependencies": {"@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}