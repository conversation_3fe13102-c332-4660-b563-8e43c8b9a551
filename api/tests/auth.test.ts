import request from 'supertest';
import app from '../src/app';

describe('Authentication Endpoints', () => {
  const testApp = app.getApp();

  describe('POST /api/auth/register', () => {
    it('should register a new user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123',
        full_name: 'Test User',
      };

      const response = await request(testApp)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('access_token');
      expect(response.body.data).toHaveProperty('refresh_token');
      expect(response.body.data.user.email).toBe(userData.email);
    });

    it('should return validation error for invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'TestPassword123',
      };

      const response = await request(testApp)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
      expect(response.body.errors).toHaveProperty('email');
    });

    it('should return validation error for weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123',
      };

      const response = await request(testApp)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
      expect(response.body.errors).toHaveProperty('password');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should return validation error for missing credentials', async () => {
      const response = await request(testApp)
        .post('/api/auth/login')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
    });

    it('should return validation error for invalid email format', async () => {
      const credentials = {
        email: 'invalid-email',
        password: 'password123',
      };

      const response = await request(testApp)
        .post('/api/auth/login')
        .send(credentials)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors).toHaveProperty('email');
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should return validation error for missing refresh token', async () => {
      const response = await request(testApp)
        .post('/api/auth/refresh')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
      expect(response.body.errors).toHaveProperty('refresh_token');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should return unauthorized error without token', async () => {
      const response = await request(testApp)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Authorization header is required');
    });

    it('should return unauthorized error with invalid token', async () => {
      const response = await request(testApp)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });
});

describe('Health Check', () => {
  const testApp = app.getApp();

  it('should return health status', async () => {
    const response = await request(testApp)
      .get('/health')
      .expect(200);

    expect(response.body).toHaveProperty('status', 'ok');
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('uptime');
    expect(response.body).toHaveProperty('environment');
  });
});
