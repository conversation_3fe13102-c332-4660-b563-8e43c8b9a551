// Test setup file
import { env } from '../src/config/env';

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup test database or mock services here if needed
beforeAll(async () => {
  // Initialize test database or mock services
});

afterAll(async () => {
  // Cleanup test database or mock services
});

beforeEach(() => {
  // Reset mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Cleanup after each test
});
