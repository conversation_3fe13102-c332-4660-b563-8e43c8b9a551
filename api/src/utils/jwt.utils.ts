import jwt from "jsonwebtoken";
import { env } from "../config/env";
import { JwtPayload, AuthenticatedUser } from "../models/user.model";

export class JwtUtils {
  /**
   * Generate access token
   */
  static generateAccessToken(user: AuthenticatedUser): string {
    const payload = {
      sub: user.id,
      email: user.email,
    };

    return (jwt.sign as any)(payload, env.JWT_SECRET, {
      expiresIn: env.JWT_EXPIRES_IN,
      issuer: "deepledger-api",
      audience: "deepledger-client",
    });
  }

  /**
   * Generate refresh token
   */
  static generateRefreshToken(user: AuthenticatedUser): string {
    const payload = {
      sub: user.id,
      email: user.email,
    };

    return (jwt.sign as any)(payload, env.JWT_SECRET, {
      expiresIn: env.JWT_REFRESH_EXPIRES_IN,
      issuer: "deepledger-api",
      audience: "deepledger-client",
    });
  }

  /**
   * Verify and decode token
   */
  static verifyToken(token: string): JwtPayload {
    try {
      const decoded = jwt.verify(token, env.JWT_SECRET as string, {
        issuer: "deepledger-api",
        audience: "deepledger-client",
      }) as JwtPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error("Token has expired");
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Invalid token");
      } else {
        throw new Error("Token verification failed");
      }
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  static decodeToken(token: string): JwtPayload | null {
    try {
      return jwt.decode(token) as JwtPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded) return true;

      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * Get token expiration time in seconds
   */
  static getTokenExpirationTime(): number {
    // Convert JWT_EXPIRES_IN to seconds
    const expiresIn = env.JWT_EXPIRES_IN;

    if (expiresIn.endsWith("h")) {
      return parseInt(expiresIn.slice(0, -1)) * 3600;
    } else if (expiresIn.endsWith("d")) {
      return parseInt(expiresIn.slice(0, -1)) * 86400;
    } else if (expiresIn.endsWith("m")) {
      return parseInt(expiresIn.slice(0, -1)) * 60;
    } else {
      return parseInt(expiresIn);
    }
  }
}
