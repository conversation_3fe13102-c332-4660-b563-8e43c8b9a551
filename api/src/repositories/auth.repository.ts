import { BaseRepository } from "./base.repository";
import { User, CreateUserRequest, LoginRequest } from "../models/user.model";

export class AuthRepository extends BaseRepository {
  /**
   * Transform Supabase auth user to our User model
   */
  private transformAuthUser(authUser: any): User {
    // Extract first_name and last_name from user_metadata
    const first_name = authUser.user_metadata?.first_name || null;
    const last_name = authUser.user_metadata?.last_name || null;

    // Compute full_name from first_name and last_name, or use existing full_name
    let full_name = authUser.user_metadata?.full_name || null;
    if (!full_name && (first_name || last_name)) {
      full_name = [first_name, last_name]
        .filter(Boolean)
        .join(' ')
        .trim() || null;
    }

    return {
      id: authUser.id,
      email: authUser.email!,
      first_name,
      last_name,
      full_name,
      avatar_url: authUser.user_metadata?.avatar_url || null,
      created_at: authUser.created_at!,
      updated_at: authUser.updated_at!,
    };
  }

  /**
   * Register a new user using Supabase Auth
   */
  async register(
    userData: CreateUserRequest
  ): Promise<{ user: User; session: any }> {
    try {
      // Prepare user metadata
      const userMetadata: any = {};

      if (userData.first_name) {
        userMetadata.first_name = userData.first_name;
      }
      if (userData.last_name) {
        userMetadata.last_name = userData.last_name;
      }

      // If full_name is provided but not first_name/last_name, split it
      if (userData.full_name && !userData.first_name && !userData.last_name) {
        const nameParts = userData.full_name.trim().split(' ');
        userMetadata.first_name = nameParts[0] || null;
        userMetadata.last_name = nameParts.slice(1).join(' ') || null;
      }

      // Also store full_name for backward compatibility
      if (userData.full_name) {
        userMetadata.full_name = userData.full_name;
      }

      const { data, error } = await this.client.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: userMetadata,
        },
      });

      if (error) {
        console.error("Registration error:", error);
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error("User registration failed");
      }

      const user = this.transformAuthUser(data.user);
      return { user, session: data.session };
    } catch (error) {
      console.error("Error in register:", error);
      throw error;
    }
  }

  /**
   * Login user using Supabase Auth
   */
  async login(
    credentials: LoginRequest
  ): Promise<{ user: User; session: any }> {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error("Login error:", error);
        throw new Error(error.message);
      }

      if (!data.user || !data.session) {
        throw new Error("Login failed");
      }

      const user = this.transformAuthUser(data.user);
      return { user, session: data.session };
    } catch (error) {
      console.error("Error in login:", error);
      throw error;
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ session: any }> {
    try {
      const { data, error } = await this.client.auth.refreshSession({
        refresh_token: refreshToken,
      });

      if (error) {
        console.error("Token refresh error:", error);
        throw new Error(error.message);
      }

      if (!data.session) {
        throw new Error("Token refresh failed");
      }

      return { session: data.session };
    } catch (error) {
      console.error("Error in refreshToken:", error);
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const { error } = await this.client.auth.signOut();

      if (error) {
        console.error("Logout error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in logout:", error);
      throw error;
    }
  }

  /**
   * Get user by access token
   */
  async getUserByToken(accessToken: string): Promise<User | null> {
    try {
      const { data, error } = await this.client.auth.getUser(accessToken);

      if (error) {
        console.error("Get user by token error:", error);
        return null;
      }

      if (!data.user) {
        return null;
      }

      return this.transformAuthUser(data.user);
    } catch (error) {
      console.error("Error in getUserByToken:", error);
      return null;
    }
  }

  /**
   * Reset password
   */
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await this.client.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL}/reset-password`,
      });

      if (error) {
        console.error("Reset password error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in resetPassword:", error);
      throw error;
    }
  }

  /**
   * Update password
   */
  async updatePassword(
    accessToken: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Set the session first
      await this.client.auth.setSession({
        access_token: accessToken,
        refresh_token: "", // We don't need refresh token for password update
      });

      const { error } = await this.client.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        console.error("Update password error:", error);
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Error in updatePassword:", error);
      throw error;
    }
  }
}
