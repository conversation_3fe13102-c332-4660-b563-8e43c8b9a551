import { BaseRepository } from "./base.repository";
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserSearchResult,
  SearchUsersRequest,
} from "../models/user.model";

export class UserRepository extends BaseRepository {
  private readonly tableName = "users";

  /**
   * Transform database user to API user format
   */
  private transformUser(dbUser: any, authUser?: any): User | null {
    if (!dbUser) return null;

    // Compute full_name from first_name and last_name
    let full_name: string | null = null;
    if (dbUser.first_name || dbUser.last_name) {
      const nameParts = [dbUser.first_name, dbUser.last_name].filter(Boolean);
      full_name = nameParts.length > 0 ? nameParts.join(' ').trim() : null;
    }

    return {
      id: dbUser.user_id || dbUser.id, // user_id is the primary key in public.users
      email: authUser?.email || dbUser.email, // email comes from auth.users
      first_name: dbUser.first_name,
      last_name: db<PERSON><PERSON>.last_name,
      full_name,
      avatar_url: dbUser.avatar_url,
      created_at: dbUser.created_at,
      updated_at: dbUser.updated_at,
    };
  }

  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    try {
      // Query public.users with user_id and join with auth.users for email
      const { data, error } = await this.client
        .from(this.tableName)
        .select(`
          user_id,
          first_name,
          last_name,
          avatar_url,
          phone,
          timezone,
          preferences,
          created_at,
          updated_at
        `)
        .eq("user_id", id)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      // Try to get email from auth.users (requires admin privileges)
      let authUser: any = null;
      try {
        const { data: authData, error: authError } = await this.client.auth.admin.getUserById(id);
        if (!authError && authData?.user) {
          authUser = authData.user;
        }
      } catch (authError) {
        console.warn('⚠️ UserRepository: Cannot access auth.users without admin privileges, using fallback');
        // Try to get email using a direct query to auth.users
        try {
          const { data: emailData, error: emailError } = await this.client
            .from('auth.users')
            .select('id, email')
            .eq('id', id)
            .single();

          if (!emailError && emailData) {
            authUser = {
              id: emailData.id,
              email: emailData.email
            };
          }
        } catch (dbError) {
          console.warn('⚠️ UserRepository: Direct auth.users query also failed');

          // Last resort: try the database function
          try {
            const { data: searchResults, error: searchError } = await this.client
              .rpc('search_users_with_emails', {
                search_query: id, // Search by user ID
                result_limit: 1
              });

            if (!searchError && searchResults && searchResults.length > 0) {
              const userData = searchResults[0];
              if (userData.user_id === id) {
                authUser = {
                  id: userData.user_id,
                  email: userData.email
                };
              }
            }
          } catch (funcError) {
            console.warn('⚠️ UserRepository: Database function also not available');
          }
        }
      }

      return this.transformUser(data, authUser);
    } catch (error) {
      console.error("Error finding user by ID:", error);
      throw error;
    }
  }

  /**
   * Find user by email (using database function to avoid admin privileges)
   */
  async findByEmail(email: string): Promise<User | null> {
    try {
      console.log('🔍 UserRepository: Finding user by email:', email);

      // Use database function to search for user by email
      // This avoids needing admin privileges to list all auth users
      const { data: searchResults, error: searchError } = await this.client
        .rpc('search_users_with_emails', {
          search_query: email.toLowerCase(),
          result_limit: 1
        });

      if (searchError) {
        console.log('⚠️ UserRepository: Database function not available, using fallback method');
        return await this.findByEmailFallback(email);
      }

      if (!searchResults || searchResults.length === 0) {
        console.log('❌ UserRepository: No user found with email:', email);
        return null;
      }

      const userData = searchResults[0];
      console.log('✅ UserRepository: Found user:', {
        user_id: userData.user_id,
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name
      });

      // Transform the result to match our User interface
      const fullName = [userData.first_name, userData.last_name].filter(Boolean).join(' ') || null;

      return {
        id: userData.user_id,
        email: userData.email,
        first_name: userData.first_name || null,
        last_name: userData.last_name || null,
        full_name: fullName,
        avatar_url: userData.avatar_url || null,
        created_at: userData.created_at,
        updated_at: userData.updated_at
      };
    } catch (error) {
      console.error("Error finding user by email:", error);
      // Try fallback method
      return await this.findByEmailFallback(email);
    }
  }

  /**
   * Fallback method to find user by email when database function is not available
   */
  private async findByEmailFallback(email: string): Promise<User | null> {
    try {
      console.log('🔄 UserRepository: Using fallback method for email:', email);

      // Since we can't search auth.users without admin privileges,
      // we'll return null and let the calling code handle user creation
      // This is safer than trying to use admin functions
      console.log('❌ UserRepository: Fallback - cannot search auth users without admin privileges');
      return null;
    } catch (error) {
      console.error("Error in findByEmailFallback:", error);
      return null;
    }
  }

  /**
   * Create a new user profile (user must already exist in auth.users)
   */
  async create(userData: CreateUserRequest & { user_id: string }): Promise<User> {
    try {
      const now = this.getCurrentTimestamp();

      // Handle full_name by splitting into first_name and last_name if provided
      let first_name = userData.first_name || null;
      let last_name = userData.last_name || null;

      if (userData.full_name && !first_name && !last_name) {
        const nameParts = userData.full_name.trim().split(' ');
        first_name = nameParts[0] || null;
        last_name = nameParts.slice(1).join(' ') || null;
      }

      const userToCreate = {
        user_id: userData.user_id, // Reference to auth.users
        first_name,
        last_name,
        created_at: now,
        updated_at: now,
      };

      const result = await this.executeSingleQuery<any>(
        this.client.from(this.tableName).insert(userToCreate).select("*"),
        "Failed to create user profile"
      );

      // Get email from auth.users
      const { data: authData } = await this.client.auth.admin.getUserById(userData.user_id);

      const transformedUser = this.transformUser(result, authData.user);
      if (!transformedUser) {
        throw new Error("Failed to transform user data");
      }
      return transformedUser;
    } catch (error) {
      console.error("Error creating user profile:", error);
      throw error;
    }
  }

  /**
   * Update user
   */
  async update(id: string, userData: UpdateUserRequest): Promise<User> {
    try {
      // Handle full_name by splitting into first_name and last_name if provided
      const updateData: any = {
        updated_at: this.getCurrentTimestamp(),
      };

      if (userData.first_name !== undefined) {
        updateData.first_name = userData.first_name;
      }
      if (userData.last_name !== undefined) {
        updateData.last_name = userData.last_name;
      }
      if (userData.avatar_url !== undefined) {
        updateData.avatar_url = userData.avatar_url;
      }

      // If full_name is provided, split it into first_name and last_name
      if (userData.full_name !== undefined) {
        if (userData.full_name) {
          const nameParts = userData.full_name.trim().split(' ');
          updateData.first_name = nameParts[0] || null;
          updateData.last_name = nameParts.slice(1).join(' ') || null;
        } else {
          updateData.first_name = null;
          updateData.last_name = null;
        }
      }

      const result = await this.executeSingleQuery<any>(
        this.client
          .from(this.tableName)
          .update(updateData)
          .eq("user_id", id) // Use user_id instead of id
          .select("*"),
        "Failed to update user"
      );

      // Get email from auth.users
      const { data: authData } = await this.client.auth.admin.getUserById(id);

      const transformedUser = this.transformUser(result, authData.user);
      if (!transformedUser) {
        throw new Error("Failed to transform user data");
      }
      return transformedUser;
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }

  /**
   * Delete user profile (does not delete from auth.users)
   */
  async delete(id: string): Promise<void> {
    try {
      await this.executeQuery(
        this.client.from(this.tableName).delete().eq("user_id", id),
        "Failed to delete user profile"
      );
    } catch (error) {
      console.error("Error deleting user profile:", error);
      throw error;
    }
  }

  /**
   * Get all users with pagination
   */
  async findAll(
    page: number = 1,
    limit: number = 10
  ): Promise<{ users: User[]; total: number }> {
    try {
      const { data, count } = await this.executePaginatedQuery<any>(
        this.client.from(this.tableName),
        page,
        limit,
        "Failed to fetch users"
      );

      const transformedUsers = data.map(user => this.transformUser(user)).filter((user): user is User => user !== null);
      return { users: transformedUsers, total: count };
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  }

  /**
   * Check if user exists by email
   */
  async existsByEmail(email: string): Promise<boolean> {
    try {
      return await this.recordExists(this.tableName, "email", email);
    } catch (error) {
      console.error("Error checking if user exists by email:", error);
      throw error;
    }
  }

  /**
   * Check if user exists by ID
   */
  async existsById(id: string): Promise<boolean> {
    try {
      return await this.recordExists(this.tableName, "user_id", id);
    } catch (error) {
      console.error("Error checking if user exists by ID:", error);
      throw error;
    }
  }

  /**
   * Search users by query (matches email, first_name, last_name)
   * Uses a database function to search across auth.users and public.users
   */
  async searchUsers(searchRequest: SearchUsersRequest): Promise<UserSearchResult[]> {
    try {
      const { query, organization_id, limit = 10 } = searchRequest;

      if (!query || query.trim().length < 2) {
        return [];
      }

      const searchTerm = query.trim().toLowerCase();

      // Use a database function to search users with emails
      // This function will join auth.users with public.users
      const { data: searchResults, error: searchError } = await this.client
        .rpc('search_users_with_emails', {
          search_query: searchTerm,
          result_limit: limit
        });

      if (searchError) {
        console.error("Error searching users:", searchError);
        // Fallback to profile-only search if the function doesn't exist
        return await this.fallbackUserSearch(searchRequest);
      }

      // Get organization members if organization_id is provided
      let organizationMembers = new Set();
      if (organization_id) {
        try {
          const { data: members, error: memberError } = await this.client
            .from('organization_users')
            .select('user_id')
            .eq('organization_id', organization_id);

          if (!memberError && members) {
            organizationMembers = new Set(members.map(m => m.user_id));
          }
        } catch (error) {
          console.warn("Error fetching organization members:", error);
        }
      }

      // Transform results
      const results: UserSearchResult[] = [];

      if (searchResults) {
        for (const user of searchResults) {
          const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || null;

          results.push({
            id: user.user_id,
            email: user.email,
            first_name: user.first_name || null,
            last_name: user.last_name || null,
            full_name: fullName,
            avatar_url: user.avatar_url || null,
            is_member: organizationMembers.has(user.user_id)
          });
        }
      }

      return results;
    } catch (error) {
      console.error("Error searching users:", error);
      // Fallback to profile-only search
      return await this.fallbackUserSearch(searchRequest);
    }
  }

  /**
   * Fallback user search when the database function is not available
   */
  private async fallbackUserSearch(searchRequest: SearchUsersRequest): Promise<UserSearchResult[]> {
    try {
      const { query, organization_id, limit = 10 } = searchRequest;
      const searchTerm = query.trim().toLowerCase();

      // Search in user profiles
      const { data: userProfiles, error: profileError } = await this.client
        .from(this.tableName)
        .select('user_id, first_name, last_name, avatar_url')
        .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%`)
        .limit(limit);

      if (profileError) {
        console.error("Error fetching user profiles:", profileError);
        return [];
      }

      // Get organization members if organization_id is provided
      let organizationMembers = new Set();
      if (organization_id) {
        try {
          const { data: members, error: memberError } = await this.client
            .from('organization_users')
            .select('user_id')
            .eq('organization_id', organization_id);

          if (!memberError && members) {
            organizationMembers = new Set(members.map(m => m.user_id));
          }
        } catch (error) {
          console.warn("Error fetching organization members:", error);
        }
      }

      const results: UserSearchResult[] = [];

      if (userProfiles) {
        for (const profile of userProfiles) {
          // Try to get the email for this user
          try {
            const user = await this.findById(profile.user_id);
            if (user) {
              results.push({
                id: user.id,
                email: user.email,
                first_name: user.first_name,
                last_name: user.last_name,
                full_name: user.full_name,
                avatar_url: user.avatar_url,
                is_member: organizationMembers.has(user.id)
              });
            }
          } catch (error) {
            // Skip users we can't get email for
            console.warn(`Could not get email for user ${profile.user_id}`);
          }

          if (results.length >= limit) {
            break;
          }
        }
      }

      return results;
    } catch (error) {
      console.error("Error in fallback user search:", error);
      return [];
    }
  }
}
