import { SupabaseClient } from "@supabase/supabase-js";
import { supabase, supabaseAdmin } from "../config/supabase";

export abstract class BaseRepository {
  protected client: SupabaseClient;
  protected adminClient: SupabaseClient;

  constructor() {
    this.client = supabase;
    this.adminClient = supabaseAdmin;
  }

  /**
   * Handle Supabase errors and convert them to meaningful messages
   */
  protected handleError(error: any): never {
    console.error("Repository error:", error);

    if (error.code === "PGRST116") {
      throw new Error("Record not found");
    }

    if (error.code === "23505") {
      throw new Error("Record already exists");
    }

    if (error.code === "23503") {
      throw new Error("Referenced record does not exist");
    }

    if (error.code === "42501") {
      throw new Error("Insufficient permissions");
    }

    throw new Error(error.message || "Database operation failed");
  }

  /**
   * Execute a query with error handling
   */
  protected async executeQuery<T>(
    queryBuilder: any,
    errorMessage: string = "Query failed"
  ): Promise<T> {
    const { data, error } = await queryBuilder;

    if (error) {
      console.error(`${errorMessage}:`, error);
      this.handleError(error);
    }

    return data as T;
  }

  /**
   * Execute a query that should return a single record
   */
  protected async executeSingleQuery<T>(
    queryBuilder: any,
    errorMessage: string = "Query failed"
  ): Promise<T> {
    const { data, error } = await queryBuilder.single();

    if (error) {
      console.error(`${errorMessage}:`, error);
      this.handleError(error);
    }

    return data as T;
  }

  /**
   * Execute a query with pagination
   */
  protected async executePaginatedQuery<T>(
    queryBuilder: any,
    page: number = 1,
    limit: number = 10,
    errorMessage: string = "Paginated query failed"
  ): Promise<{ data: T[]; count: number }> {
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data, error, count } = await queryBuilder
      .range(from, to)
      .select("*", { count: "exact" });

    if (error) {
      console.error(`${errorMessage}:`, error);
      this.handleError(error);
    }

    return { data: data as T[], count: count || 0 };
  }

  /**
   * Check if a record exists
   */
  protected async recordExists(
    table: string,
    column: string,
    value: any
  ): Promise<boolean> {
    const { data, error } = await this.client
      .from(table)
      .select("id")
      .eq(column, value)
      .single();

    if (error && error.code !== "PGRST116") {
      this.handleError(error);
    }

    return !!data;
  }

  /**
   * Get current timestamp
   */
  protected getCurrentTimestamp(): string {
    return new Date().toISOString();
  }
}
