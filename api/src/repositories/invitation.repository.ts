import { BaseRepository } from "./base.repository";
import {
  OrganizationInvitation,
  InviteUserRequest,
  InvitationResponse,
  InvitationVerification,
} from "../models/user.model";
import { EmailService } from "../services/email.service";

export class InvitationRepository extends BaseRepository {
  private invitationTable = "organization_invitations";

  /**
   * Create a new organization invitation
   */
  async createInvitation(
    organizationId: string,
    inviteData: InviteUserRequest,
    invitedBy: string
  ): Promise<OrganizationInvitation> {
    try {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

      const verificationToken = EmailService.generateVerificationToken();

      const { data, error } = await this.client
        .from(this.invitationTable)
        .insert({
          organization_id: organizationId,
          email: inviteData.email.toLowerCase(),
          role: inviteData.role,
          message: inviteData.message,
          invited_by: invitedBy,
          expires_at: expiresAt.toISOString(),
          verification_token: verificationToken,
        })
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error creating invitation:", error);
      throw error;
    }
  }

  /**
   * Get invitation by ID
   */
  async findById(invitationId: string): Promise<OrganizationInvitation | null> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("*")
        .eq("invitation_id", invitationId)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error finding invitation by ID:", error);
      throw error;
    }
  }

  /**
   * Get organization invitations
   */
  async getOrganizationInvitations(organizationId: string): Promise<OrganizationInvitation[]> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("*")
        .eq("organization_id", organizationId)
        .order("invited_at", { ascending: false });

      if (error) {
        this.handleError(error);
      }

      return data || [];
    } catch (error) {
      console.error("Error getting organization invitations:", error);
      throw error;
    }
  }

  /**
   * Get pending invitations for an email
   */
  async getPendingInvitationsForEmail(email: string): Promise<OrganizationInvitation[]> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select(`
          *,
          organizations (
            organization_id,
            name,
            logo
          )
        `)
        .eq("email", email.toLowerCase())
        .eq("status", "pending")
        .gt("expires_at", new Date().toISOString())
        .order("invited_at", { ascending: false });

      if (error) {
        this.handleError(error);
      }

      return data || [];
    } catch (error) {
      console.error("Error getting pending invitations for email:", error);
      throw error;
    }
  }

  /**
   * Check if invitation exists for email and organization
   */
  async hasExistingInvitation(organizationId: string, email: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("invitation_id")
        .eq("organization_id", organizationId)
        .eq("email", email.toLowerCase())
        .eq("status", "pending")
        .gt("expires_at", new Date().toISOString())
        .single();

      if (error && error.code === "PGRST116") {
        return false;
      }

      if (error) {
        this.handleError(error);
      }

      return !!data;
    } catch (error) {
      console.error("Error checking existing invitation:", error);
      return false;
    }
  }

  /**
   * Update invitation status
   */
  async updateInvitationStatus(
    invitationId: string,
    status: 'accepted' | 'declined' | 'expired'
  ): Promise<OrganizationInvitation> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .update({
          status,
          responded_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("invitation_id", invitationId)
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error updating invitation status:", error);
      throw error;
    }
  }

  /**
   * Cancel invitation (delete it)
   */
  async cancelInvitation(invitationId: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.invitationTable)
        .delete()
        .eq("invitation_id", invitationId);

      if (error) {
        this.handleError(error);
      }
    } catch (error) {
      console.error("Error canceling invitation:", error);
      throw error;
    }
  }

  /**
   * Expire old invitations (cleanup job)
   */
  async expireOldInvitations(): Promise<number> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .update({
          status: 'expired',
          updated_at: new Date().toISOString(),
        })
        .eq("status", "pending")
        .lt("expires_at", new Date().toISOString())
        .select("invitation_id");

      if (error) {
        this.handleError(error);
      }

      return data?.length || 0;
    } catch (error) {
      console.error("Error expiring old invitations:", error);
      throw error;
    }
  }

  /**
   * Get invitation statistics for organization
   */
  async getInvitationStats(organizationId: string): Promise<{
    total: number;
    pending: number;
    accepted: number;
    declined: number;
    expired: number;
  }> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("status")
        .eq("organization_id", organizationId);

      if (error) {
        this.handleError(error);
      }

      const stats = {
        total: data?.length || 0,
        pending: 0,
        accepted: 0,
        declined: 0,
        expired: 0,
      };

      if (data) {
        data.forEach(invitation => {
          stats[invitation.status as keyof typeof stats]++;
        });
      }

      return stats;
    } catch (error) {
      console.error("Error getting invitation stats:", error);
      throw error;
    }
  }

  /**
   * Find invitation by verification token
   */
  async findByVerificationToken(token: string): Promise<OrganizationInvitation | null> {
    try {
      console.log('🔍 Searching for invitation with token:', token);

      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("*")
        .eq("verification_token", token)
        .single();

      if (error && error.code === "PGRST116") {
        console.log('❌ No invitation found with token:', token);
        return null;
      }

      if (error) {
        console.error('❌ Database error finding invitation:', error);
        this.handleError(error);
      }

      if (data) {
        console.log('✅ Found invitation:', {
          id: data.invitation_id,
          email: data.email,
          status: data.status,
          expires_at: data.expires_at
        });
      }

      return data;
    } catch (error) {
      console.error("Error finding invitation by token:", error);
      throw error;
    }
  }

  /**
   * Get invitation verification details
   */
  async getInvitationVerification(token: string): Promise<InvitationVerification | null> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select(`
          *,
          organizations (
            organization_id,
            name,
            logo
          )
        `)
        .eq("verification_token", token)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      if (!data) {
        return null;
      }

      return {
        invitation: data,
        organization: data.organizations,
        inviter: {
          full_name: null, // Will be populated by the service layer
          email: 'Unknown' // Will be populated by the service layer
        },
        user_exists: false // Will be set by the service
      };
    } catch (error) {
      console.error("Error getting invitation verification:", error);
      throw error;
    }
  }

  /**
   * Debug method to list all invitations (for development)
   */
  async debugListAllInvitations(): Promise<OrganizationInvitation[]> {
    try {
      const { data, error } = await this.client
        .from(this.invitationTable)
        .select("*")
        .order("invited_at", { ascending: false });

      if (error) {
        this.handleError(error);
      }

      return data || [];
    } catch (error) {
      console.error("Error listing all invitations:", error);
      throw error;
    }
  }
}
