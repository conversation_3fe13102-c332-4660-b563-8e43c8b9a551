import { BaseRepository } from "./base.repository";
import {
  Organization,
  OrganizationUser,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  AddOrganizationUserRequest,
  UpdateOrganizationUserRequest,
  OrganizationWithRole,
  OrganizationMember,
} from "../models/user.model";

export class OrganizationRepository extends BaseRepository {
  private organizationTable = "organizations";
  private organizationUsersTable = "organization_users";

  /**
   * Create a new organization
   */
  async create(
    organizationData: CreateOrganizationRequest,
    createdBy: string
  ): Promise<Organization> {
    try {
      // Generate slug if not provided
      const slug = organizationData.slug || this.generateSlug(organizationData.name);

      const { data, error } = await this.client
        .from(this.organizationTable)
        .insert({
          name: organizationData.name,
          slug,
          logo: organizationData.logo,
          settings: organizationData.settings || {},
          created_by: createdBy,
          updated_by: createdBy,
        })
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error creating organization:", error);
      throw error;
    }
  }

  /**
   * Find organization by ID
   */
  async findById(organizationId: string): Promise<Organization | null> {
    try {
      const { data, error } = await this.client
        .from(this.organizationTable)
        .select("*")
        .eq("organization_id", organizationId)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error finding organization by ID:", error);
      throw error;
    }
  }

  /**
   * Find organization by slug
   */
  async findBySlug(slug: string): Promise<Organization | null> {
    try {
      const { data, error } = await this.client
        .from(this.organizationTable)
        .select("*")
        .eq("slug", slug)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error finding organization by slug:", error);
      throw error;
    }
  }

  /**
   * Update organization
   */
  async update(
    organizationId: string,
    updateData: UpdateOrganizationRequest,
    updatedBy: string
  ): Promise<Organization> {
    try {
      const { data, error } = await this.client
        .from(this.organizationTable)
        .update({
          ...updateData,
          updated_by: updatedBy,
          updated_at: new Date().toISOString(),
        })
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error updating organization:", error);
      throw error;
    }
  }

  /**
   * Delete organization
   */
  async delete(organizationId: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.organizationTable)
        .delete()
        .eq("organization_id", organizationId);

      if (error) {
        this.handleError(error);
      }
    } catch (error) {
      console.error("Error deleting organization:", error);
      throw error;
    }
  }

  /**
   * Get user's organizations with their role
   */
  async getUserOrganizations(userId: string): Promise<OrganizationWithRole[]> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .select(`
          role,
          organizations (
            organization_id,
            name,
            slug,
            logo,
            settings,
            created_at,
            updated_at,
            created_by,
            updated_by
          )
        `)
        .eq("user_id", userId);

      if (error) {
        this.handleError(error);
      }

      return data.map((item: any) => ({
        ...item.organizations,
        user_role: item.role,
      }));
    } catch (error) {
      console.error("Error getting user organizations:", error);
      throw error;
    }
  }

  /**
   * Add user to organization
   */
  async addUser(
    organizationId: string,
    userData: AddOrganizationUserRequest
  ): Promise<OrganizationUser> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .insert({
          organization_id: organizationId,
          user_id: userData.user_id,
          role: userData.role,
        })
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error adding user to organization:", error);
      throw error;
    }
  }

  /**
   * Update user role in organization
   */
  async updateUserRole(
    organizationId: string,
    userId: string,
    updateData: UpdateOrganizationUserRequest
  ): Promise<OrganizationUser> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .update({
          role: updateData.role,
          updated_at: new Date().toISOString(),
        })
        .eq("organization_id", organizationId)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error updating user role:", error);
      throw error;
    }
  }

  /**
   * Remove user from organization
   */
  async removeUser(organizationId: string, userId: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.organizationUsersTable)
        .delete()
        .eq("organization_id", organizationId)
        .eq("user_id", userId);

      if (error) {
        this.handleError(error);
      }
    } catch (error) {
      console.error("Error removing user from organization:", error);
      throw error;
    }
  }

  /**
   * Get organization members
   */
  async getMembers(organizationId: string): Promise<OrganizationMember[]> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .select(`
          organization_user_id,
          user_id,
          role,
          created_at,
          updated_at
        `)
        .eq("organization_id", organizationId);

      if (error) {
        this.handleError(error);
      }

      // Get user details for each member from both auth.users and public.users
      const membersWithUserData = await Promise.all(
        data.map(async (member: any) => {
          try {
            // Get email from auth.users
            const { data: authData } = await this.client.auth.admin.getUserById(member.user_id);

            // Get profile data from public.users
            const { data: profileData } = await this.client
              .from('users')
              .select('first_name, last_name, avatar_url')
              .eq('user_id', member.user_id)
              .single();

            // Construct full_name from first_name and last_name
            let full_name: string | null = null;
            if (profileData?.first_name || profileData?.last_name) {
              const nameParts = [profileData.first_name, profileData.last_name].filter(Boolean);
              full_name = nameParts.length > 0 ? nameParts.join(' ') : null;
            }

            return {
              ...member,
              user: {
                email: authData.user?.email || 'Unknown',
                full_name: full_name || authData.user?.user_metadata?.full_name || null,
                avatar_url: profileData?.avatar_url || authData.user?.user_metadata?.avatar_url || null,
              },
            };
          } catch (error) {
            console.warn(`Failed to get user data for ${member.user_id}:`, error);
            return {
              ...member,
              user: {
                email: 'Unknown',
                full_name: null,
                avatar_url: null,
              },
            };
          }
        })
      );

      return membersWithUserData;
    } catch (error) {
      console.error("Error getting organization members:", error);
      throw error;
    }
  }

  /**
   * Get organization member statistics
   */
  async getMemberStats(organizationId: string): Promise<{
    total_members: number;
    role_breakdown: Record<string, number>;
    recent_joins: number;
  }> {
    try {
      // Get all members
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .select("role, created_at")
        .eq("organization_id", organizationId);

      if (error) {
        this.handleError(error);
      }

      const total_members = data.length;

      // Calculate role breakdown
      const role_breakdown = data.reduce((acc: Record<string, number>, member: any) => {
        acc[member.role] = (acc[member.role] || 0) + 1;
        return acc;
      }, {});

      // Calculate recent joins (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recent_joins = data.filter((member: any) =>
        new Date(member.created_at) > thirtyDaysAgo
      ).length;

      return {
        total_members,
        role_breakdown,
        recent_joins
      };
    } catch (error) {
      console.error("Error getting organization member stats:", error);
      throw error;
    }
  }

  /**
   * Check if user has access to organization
   */
  async hasUserAccess(organizationId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .select("organization_user_id")
        .eq("organization_id", organizationId)
        .eq("user_id", userId)
        .single();

      if (error && error.code === "PGRST116") {
        return false;
      }

      if (error) {
        this.handleError(error);
      }

      return !!data;
    } catch (error) {
      console.error("Error checking user access:", error);
      return false;
    }
  }

  /**
   * Get user role in organization
   */
  async getUserRole(organizationId: string, userId: string): Promise<string | null> {
    try {
      const { data, error } = await this.client
        .from(this.organizationUsersTable)
        .select("role")
        .eq("organization_id", organizationId)
        .eq("user_id", userId)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data.role;
    } catch (error) {
      console.error("Error getting user role:", error);
      return null;
    }
  }

  /**
   * Generate slug from organization name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
}
