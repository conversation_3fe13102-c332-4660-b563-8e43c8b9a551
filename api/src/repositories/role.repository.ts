import { BaseRepository } from "./base.repository";
import { Role, CreateRoleRequest, UpdateRoleRequest } from "../models/role.model";

export class RoleRepository extends BaseRepository {
  private roleTable = "roles";

  /**
   * Get all roles
   */
  async findAll(): Promise<Role[]> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .select("*")
        .order("name");

      if (error) {
        this.handleError(error);
      }

      return data.map(role => ({
        ...role,
        permissions: Array.isArray(role.permissions) ? role.permissions : []
      }));
    } catch (error) {
      console.error("Error getting all roles:", error);
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  async findById(roleId: string): Promise<Role | null> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .select("*")
        .eq("role_id", roleId)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return {
        ...data,
        permissions: Array.isArray(data.permissions) ? data.permissions : []
      };
    } catch (error) {
      console.error("Error getting role by ID:", error);
      return null;
    }
  }

  /**
   * Get role by name
   */
  async findByName(name: string): Promise<Role | null> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .select("*")
        .eq("name", name)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return {
        ...data,
        permissions: Array.isArray(data.permissions) ? data.permissions : []
      };
    } catch (error) {
      console.error("Error getting role by name:", error);
      return null;
    }
  }

  /**
   * Get system roles only
   */
  async findSystemRoles(): Promise<Role[]> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .select("*")
        .eq("is_system_role", true)
        .order("name");

      if (error) {
        this.handleError(error);
      }

      return data.map(role => ({
        ...role,
        permissions: Array.isArray(role.permissions) ? role.permissions : []
      }));
    } catch (error) {
      console.error("Error getting system roles:", error);
      throw error;
    }
  }

  /**
   * Create a new role (for custom roles)
   */
  async create(roleData: CreateRoleRequest): Promise<Role> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .insert({
          name: roleData.name,
          display_name: roleData.display_name,
          description: roleData.description,
          permissions: roleData.permissions || [],
          is_system_role: roleData.is_system_role || false,
        })
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return {
        ...data,
        permissions: Array.isArray(data.permissions) ? data.permissions : []
      };
    } catch (error) {
      console.error("Error creating role:", error);
      throw error;
    }
  }

  /**
   * Update role (only custom roles can be updated)
   */
  async update(roleId: string, updateData: UpdateRoleRequest): Promise<Role> {
    try {
      const { data, error } = await this.client
        .from(this.roleTable)
        .update({
          display_name: updateData.display_name,
          description: updateData.description,
          permissions: updateData.permissions,
          updated_at: new Date().toISOString(),
        })
        .eq("role_id", roleId)
        .eq("is_system_role", false) // Only allow updating custom roles
        .select()
        .single();

      if (error) {
        this.handleError(error);
      }

      return {
        ...data,
        permissions: Array.isArray(data.permissions) ? data.permissions : []
      };
    } catch (error) {
      console.error("Error updating role:", error);
      throw error;
    }
  }

  /**
   * Delete role (only custom roles can be deleted)
   */
  async delete(roleId: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.roleTable)
        .delete()
        .eq("role_id", roleId)
        .eq("is_system_role", false); // Only allow deleting custom roles

      if (error) {
        this.handleError(error);
      }
    } catch (error) {
      console.error("Error deleting role:", error);
      throw error;
    }
  }
}
