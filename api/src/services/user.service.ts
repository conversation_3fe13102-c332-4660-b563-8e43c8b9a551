import { UserRepository } from "../repositories/user.repository";
import {
  User,
  UpdateUserRequest,
  UserProfile,
  PaginatedResponse,
} from "../models/user.model";
import {
  CustomError,
  NotFoundError,
  ValidationError,
  ConflictError,
} from "../middleware/error.middleware";

export class UserService {
  private userRepository: UserRepository;

  constructor() {
    this.userRepository = new UserRepository();
  }

  /**
   * Get user profile by ID
   */
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError("User not found");
      }

      return this.mapUserToProfile(user);
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Failed to get user profile",
        500
      );
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    userId: string,
    updateData: UpdateUserRequest
  ): Promise<UserProfile> {
    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(userId);
      if (!existingUser) {
        throw new NotFoundError("User not found");
      }

      // Validate update data
      this.validateUpdateData(updateData);

      // Update user
      const updatedUser = await this.userRepository.update(userId, updateData);

      return this.mapUserToProfile(updatedUser);
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error
          ? error.message
          : "Failed to update user profile",
        500
      );
    }
  }

  /**
   * Get all users with pagination (admin only)
   */
  async getAllUsers(
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<UserProfile>> {
    try {
      // Validate pagination parameters
      if (page < 1) {
        throw new ValidationError("Page must be greater than 0");
      }
      if (limit < 1 || limit > 100) {
        throw new ValidationError("Limit must be between 1 and 100");
      }

      const { users, total } = await this.userRepository.findAll(page, limit);

      const profiles = users.map((user) => this.mapUserToProfile(user));
      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: profiles,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Failed to get users",
        500
      );
    }
  }

  /**
   * Delete user account
   */
  async deleteUser(userId: string): Promise<void> {
    try {
      // Check if user exists
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError("User not found");
      }

      await this.userRepository.delete(userId);
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Failed to delete user",
        500
      );
    }
  }

  /**
   * Search users by email (admin only)
   */
  async searchUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      if (!email || !this.isValidEmail(email)) {
        throw new ValidationError("Valid email is required");
      }

      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        return null;
      }

      return this.mapUserToProfile(user);
    } catch (error) {
      if (error instanceof CustomError) {
        throw error;
      }
      throw new CustomError(
        error instanceof Error ? error.message : "Failed to search user",
        500
      );
    }
  }

  /**
   * Check if user exists by email
   */
  async userExistsByEmail(email: string): Promise<boolean> {
    try {
      if (!email || !this.isValidEmail(email)) {
        return false;
      }

      return await this.userRepository.existsByEmail(email);
    } catch (error) {
      console.error("Error checking if user exists:", error);
      return false;
    }
  }

  /**
   * Get user statistics (admin only)
   */
  async getUserStatistics(): Promise<{
    totalUsers: number;
    newUsersToday: number;
    newUsersThisWeek: number;
    newUsersThisMonth: number;
  }> {
    try {
      // This is a simplified implementation
      // In a real application, you might want to add specific queries for these statistics
      const { users: allUsers } = await this.userRepository.findAll(1, 1000);

      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const newUsersToday = allUsers.filter(
        (user) => new Date(user.created_at) >= today
      ).length;

      const newUsersThisWeek = allUsers.filter(
        (user) => new Date(user.created_at) >= weekAgo
      ).length;

      const newUsersThisMonth = allUsers.filter(
        (user) => new Date(user.created_at) >= monthAgo
      ).length;

      return {
        totalUsers: allUsers.length,
        newUsersToday,
        newUsersThisWeek,
        newUsersThisMonth,
      };
    } catch (error) {
      throw new CustomError(
        error instanceof Error
          ? error.message
          : "Failed to get user statistics",
        500
      );
    }
  }

  /**
   * Map User to UserProfile
   */
  private mapUserToProfile(user: User): UserProfile {
    return {
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      avatar_url: user.avatar_url,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };
  }

  /**
   * Validate update data
   */
  private validateUpdateData(updateData: UpdateUserRequest): void {
    const errors: Record<string, string[]> = {};

    if (updateData.full_name !== undefined) {
      if (typeof updateData.full_name !== "string") {
        errors.full_name = ["Full name must be a string"];
      } else if (updateData.full_name.length > 100) {
        errors.full_name = ["Full name must be less than 100 characters"];
      }
    }

    if (updateData.avatar_url !== undefined) {
      if (typeof updateData.avatar_url !== "string") {
        errors.avatar_url = ["Avatar URL must be a string"];
      } else if (
        updateData.avatar_url &&
        !this.isValidUrl(updateData.avatar_url)
      ) {
        errors.avatar_url = ["Avatar URL must be a valid URL"];
      }
    }

    if (Object.keys(errors).length > 0) {
      throw new ValidationError("Validation failed", errors);
    }
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
