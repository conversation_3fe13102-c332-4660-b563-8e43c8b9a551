import { OrganizationRepository } from "../repositories/organization.repository";
import {
  Organization,
  OrganizationUser,
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  AddOrganizationUserRequest,
  UpdateOrganizationUserRequest,
  OrganizationWithRole,
  OrganizationMember,
} from "../models/user.model";

export class OrganizationService {
  private organizationRepository: OrganizationRepository;

  constructor() {
    this.organizationRepository = new OrganizationRepository();
  }

  /**
   * Create a new organization and make the creator an owner
   */
  async createOrganization(
    organizationData: CreateOrganizationRequest,
    createdBy: string
  ): Promise<Organization> {
    try {
      // Check if slug is already taken
      if (organizationData.slug) {
        const existingOrg = await this.organizationRepository.findBySlug(organizationData.slug);
        if (existingOrg) {
          throw new Error("Organization slug already exists");
        }
      }

      // Create organization
      const organization = await this.organizationRepository.create(organizationData, createdBy);

      // Add creator as owner
      await this.organizationRepository.addUser(organization.organization_id, {
        user_id: createdBy,
        role: 'owner' as any, // Cast to satisfy type constraint
      });

      return organization;
    } catch (error) {
      console.error("Error in createOrganization:", error);
      throw error;
    }
  }

  /**
   * Get organization by ID with permission check
   */
  async getOrganization(organizationId: string, userId: string): Promise<Organization> {
    try {
      // Check if user has access
      const hasAccess = await this.organizationRepository.hasUserAccess(organizationId, userId);
      if (!hasAccess) {
        throw new Error("Access denied to organization");
      }

      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      return organization;
    } catch (error) {
      console.error("Error in getOrganization:", error);
      throw error;
    }
  }

  /**
   * Update organization with permission check
   */
  async updateOrganization(
    organizationId: string,
    updateData: UpdateOrganizationRequest,
    userId: string
  ): Promise<Organization> {
    try {
      // Check if user has admin or owner role
      const userRole = await this.organizationRepository.getUserRole(organizationId, userId);
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        throw new Error("Insufficient permissions to update organization");
      }

      // Check if slug is already taken (if being updated)
      if (updateData.slug) {
        const existingOrg = await this.organizationRepository.findBySlug(updateData.slug);
        if (existingOrg && existingOrg.organization_id !== organizationId) {
          throw new Error("Organization slug already exists");
        }
      }

      return await this.organizationRepository.update(organizationId, updateData, userId);
    } catch (error) {
      console.error("Error in updateOrganization:", error);
      throw error;
    }
  }

  /**
   * Delete organization with permission check
   */
  async deleteOrganization(organizationId: string, userId: string): Promise<void> {
    try {
      // Check if user is owner
      const userRole = await this.organizationRepository.getUserRole(organizationId, userId);
      if (userRole !== 'owner') {
        throw new Error("Only organization owners can delete organizations");
      }

      await this.organizationRepository.delete(organizationId);
    } catch (error) {
      console.error("Error in deleteOrganization:", error);
      throw error;
    }
  }

  /**
   * Get user's organizations
   */
  async getUserOrganizations(userId: string): Promise<OrganizationWithRole[]> {
    try {
      return await this.organizationRepository.getUserOrganizations(userId);
    } catch (error) {
      console.error("Error in getUserOrganizations:", error);
      throw error;
    }
  }

  /**
   * Add user to organization with permission check
   */
  async addUserToOrganization(
    organizationId: string,
    userData: AddOrganizationUserRequest,
    requestingUserId: string
  ): Promise<OrganizationUser> {
    try {
      // Check if requesting user has admin or owner role
      const requestingUserRole = await this.organizationRepository.getUserRole(
        organizationId,
        requestingUserId
      );
      if (!requestingUserRole || !['owner', 'admin'].includes(requestingUserRole)) {
        throw new Error("Insufficient permissions to add users");
      }

      // Check if user is already in organization
      const hasAccess = await this.organizationRepository.hasUserAccess(
        organizationId,
        userData.user_id
      );
      if (hasAccess) {
        throw new Error("User is already a member of this organization");
      }

      return await this.organizationRepository.addUser(organizationId, userData);
    } catch (error) {
      console.error("Error in addUserToOrganization:", error);
      throw error;
    }
  }

  /**
   * Update user role in organization with permission check
   */
  async updateUserRole(
    organizationId: string,
    targetUserId: string,
    updateData: UpdateOrganizationUserRequest,
    requestingUserId: string
  ): Promise<OrganizationUser> {
    try {
      // Check if requesting user has admin or owner role
      const requestingUserRole = await this.organizationRepository.getUserRole(
        organizationId,
        requestingUserId
      );
      if (!requestingUserRole || !['owner', 'admin'].includes(requestingUserRole)) {
        throw new Error("Insufficient permissions to update user roles");
      }

      // Get target user's current role
      const targetUserRole = await this.organizationRepository.getUserRole(
        organizationId,
        targetUserId
      );
      if (!targetUserRole) {
        throw new Error("User is not a member of this organization");
      }

      // Prevent non-owners from modifying owner roles
      if (targetUserRole === 'owner' && requestingUserRole !== 'owner') {
        throw new Error("Only owners can modify owner roles");
      }

      // Prevent users from changing their own role
      if (requestingUserId === targetUserId) {
        throw new Error("Cannot change your own role");
      }

      return await this.organizationRepository.updateUserRole(
        organizationId,
        targetUserId,
        updateData
      );
    } catch (error) {
      console.error("Error in updateUserRole:", error);
      throw error;
    }
  }

  /**
   * Remove user from organization with permission check
   */
  async removeUserFromOrganization(
    organizationId: string,
    targetUserId: string,
    requestingUserId: string
  ): Promise<void> {
    try {
      // Check if requesting user has admin or owner role
      const requestingUserRole = await this.organizationRepository.getUserRole(
        organizationId,
        requestingUserId
      );
      if (!requestingUserRole || !['owner', 'admin'].includes(requestingUserRole)) {
        throw new Error("Insufficient permissions to remove users");
      }

      // Get target user's current role
      const targetUserRole = await this.organizationRepository.getUserRole(
        organizationId,
        targetUserId
      );
      if (!targetUserRole) {
        throw new Error("User is not a member of this organization");
      }

      // Prevent non-owners from removing owners
      if (targetUserRole === 'owner' && requestingUserRole !== 'owner') {
        throw new Error("Only owners can remove other owners");
      }

      // Prevent users from removing themselves (they should use leave organization)
      if (requestingUserId === targetUserId) {
        throw new Error("Use leave organization to remove yourself");
      }

      await this.organizationRepository.removeUser(organizationId, targetUserId);
    } catch (error) {
      console.error("Error in removeUserFromOrganization:", error);
      throw error;
    }
  }

  /**
   * Leave organization (user removes themselves)
   */
  async leaveOrganization(organizationId: string, userId: string): Promise<void> {
    try {
      const userRole = await this.organizationRepository.getUserRole(organizationId, userId);
      if (!userRole) {
        throw new Error("You are not a member of this organization");
      }

      // Check if user is the only owner
      if (userRole === 'owner') {
        const members = await this.organizationRepository.getMembers(organizationId);
        const owners = members.filter(member => member.role === 'owner');
        if (owners.length === 1) {
          throw new Error("Cannot leave organization as the only owner. Transfer ownership first.");
        }
      }

      await this.organizationRepository.removeUser(organizationId, userId);
    } catch (error) {
      console.error("Error in leaveOrganization:", error);
      throw error;
    }
  }

  /**
   * Get organization members with permission check
   */
  async getOrganizationMembers(
    organizationId: string,
    requestingUserId: string
  ): Promise<OrganizationMember[]> {
    try {
      // Check if user has access to organization
      const hasAccess = await this.organizationRepository.hasUserAccess(
        organizationId,
        requestingUserId
      );
      if (!hasAccess) {
        throw new Error("Access denied to organization");
      }

      return await this.organizationRepository.getMembers(organizationId);
    } catch (error) {
      console.error("Error in getOrganizationMembers:", error);
      throw error;
    }
  }

  /**
   * Get organization member statistics
   */
  async getOrganizationMemberStats(
    organizationId: string,
    requestingUserId: string
  ): Promise<{
    total_members: number;
    role_breakdown: Record<string, number>;
    recent_joins: number;
  }> {
    try {
      // Check if user has access to organization
      const hasAccess = await this.organizationRepository.hasUserAccess(
        organizationId,
        requestingUserId
      );
      if (!hasAccess) {
        throw new Error("Access denied to organization");
      }

      return await this.organizationRepository.getMemberStats(organizationId);
    } catch (error) {
      console.error("Error in getOrganizationMemberStats:", error);
      throw error;
    }
  }

  /**
   * Check if user has specific permission in organization
   */
  async hasPermission(
    organizationId: string,
    userId: string,
    permission: 'read' | 'write' | 'admin' | 'owner'
  ): Promise<boolean> {
    try {
      const userRole = await this.organizationRepository.getUserRole(organizationId, userId);
      if (!userRole) {
        return false;
      }

      const roleHierarchy = {
        viewer: ['read'],
        member: ['read', 'write'],
        accountant: ['read', 'write', 'financial'],
        admin: ['read', 'write', 'admin'],
        owner: ['read', 'write', 'admin', 'owner'],
      };

      return roleHierarchy[userRole as keyof typeof roleHierarchy]?.includes(permission) || false;
    } catch (error) {
      console.error("Error in hasPermission:", error);
      return false;
    }
  }
}
