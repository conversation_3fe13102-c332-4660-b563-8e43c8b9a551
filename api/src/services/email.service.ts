import { BaseRepository } from "../repositories/base.repository";
import { OrganizationInvitation } from "../models/user.model";
import sgMail from '@sendgrid/mail';

interface EmailTemplate {
  template_id: string;
  name: string;
  subject: string;
  html_content: string;
  text_content?: string;
  variables: Record<string, any>;
}

interface EmailVariables {
  organization_name: string;
  inviter_name: string;
  inviter_email: string;
  role: string;
  role_description: string;
  message?: string;
  invitation_link: string;
  expiry_date: string;
}

export class EmailService extends BaseRepository {
  private templatesTable = "email_templates";

  constructor() {
    super();
    // Initialize SendGrid with API key
    const sendGridApiKey = process.env.SENDGRID_API_KEY;
    if (sendGridApiKey) {
      sgMail.setApiKey(sendGridApiKey);
      console.log('📧 SendGrid initialized successfully');
    } else {
      console.warn('⚠️ SENDGRID_API_KEY not found, email sending will be simulated');
    }
  }

  /**
   * Get email template by name
   */
  async getTemplate(templateName: string): Promise<EmailTemplate | null> {
    try {
      const { data, error } = await this.client
        .from(this.templatesTable)
        .select("*")
        .eq("name", templateName)
        .eq("is_active", true)
        .single();

      if (error && error.code === "PGRST116") {
        return null;
      }

      if (error) {
        this.handleError(error);
      }

      return data;
    } catch (error) {
      console.error("Error getting email template:", error);
      throw error;
    }
  }

  /**
   * Replace template variables with actual values (generic version)
   */
  private replaceVariables(content: string, variables: Record<string, any>): string {
    let result = content;

    // Replace simple variables
    Object.entries(variables).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        const regex = new RegExp(`{{${key}}}`, 'g');
        result = result.replace(regex, String(value));
      }
    });

    // Handle conditional blocks (simple implementation)
    // {{#if variableName}} content {{/if}}
    Object.keys(variables).forEach(key => {
      const value = variables[key];
      if (value) {
        // Replace conditional blocks that should be shown
        const showRegex = new RegExp(`{{#if ${key}}}([\\s\\S]*?){{/if}}`, 'g');
        result = result.replace(showRegex, '$1');
      } else {
        // Remove conditional blocks that should be hidden
        const hideRegex = new RegExp(`{{#if ${key}}}[\\s\\S]*?{{/if}}`, 'g');
        result = result.replace(hideRegex, '');
      }
    });

    return result;
  }

  /**
   * Get role description
   */
  private getRoleDescription(role: string): string {
    switch (role) {
      case 'admin':
        return 'Can manage organization settings, invite users, and access all features';
      case 'member':
        return 'Can view and edit organization data, create transactions and reports';
      case 'viewer':
        return 'Can view organization data and reports (read-only access)';
      default:
        return 'Organization member';
    }
  }

  /**
   * Generic method to send email using template
   */
  async sendTemplatedEmail(
    templateName: string,
    to: string,
    variables: Record<string, any>,
    options?: {
      fromEmail?: string;
      fromName?: string;
    }
  ): Promise<void> {
    try {
      // Get email template from database
      const template = await this.getTemplate(templateName);
      if (!template) {
        throw new Error(`Email template '${templateName}' not found`);
      }

      // Process template with variables
      const processedTemplate = this.processTemplate(template, variables);

      // Send email
      await this.sendRealEmail({
        to,
        subject: processedTemplate.subject,
        html: processedTemplate.html,
        text: processedTemplate.text,
        fromEmail: options?.fromEmail,
        fromName: options?.fromName,
        invitationLink: variables.invitation_link || undefined
      });

    } catch (error) {
      console.error(`Error sending templated email '${templateName}':`, error);
      throw error;
    }
  }

  /**
   * Process email template with variables
   */
  private processTemplate(
    template: EmailTemplate,
    variables: Record<string, any>
  ): {
    subject: string;
    html: string;
    text?: string;
  } {
    // Replace variables in template content
    const subject = this.replaceVariables(template.subject, variables);
    const html = this.replaceVariables(template.html_content, variables);
    const text = template.text_content
      ? this.replaceVariables(template.text_content, variables)
      : undefined;

    return { subject, html, text };
  }

  /**
   * Send organization invitation email (using generic template method)
   */
  async sendInvitationEmail(
    invitation: OrganizationInvitation,
    organizationName: string,
    inviterName: string,
    inviterEmail: string,
    baseUrl: string
  ): Promise<void> {
    try {
      // Prepare template variables
      const invitationLink = `${baseUrl}/invitation/verify?token=${invitation.verification_token}`;
      const expiryDate = new Date(invitation.expires_at).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });

      const variables: EmailVariables = {
        organization_name: organizationName,
        inviter_name: inviterName,
        inviter_email: inviterEmail,
        role: invitation.role,
        role_description: this.getRoleDescription(invitation.role),
        message: invitation.message || undefined,
        invitation_link: invitationLink,
        expiry_date: expiryDate
      };

      try {
        // Try to send email using database template
        await this.sendTemplatedEmail(
          'organization_invitation',
          invitation.email,
          variables,
          {
            fromEmail: process.env.SENDGRID_FROM_EMAIL,
            fromName: 'DeepLedger'
          }
        );
      } catch (templateError) {
        console.log('📧 Database template not found, using fallback email template');

        // Fallback to hardcoded template
        await this.sendInvitationEmailFallback(
          invitation.email,
          organizationName,
          inviterName,
          invitation.role,
          invitationLink,
          expiryDate,
          invitation.message || undefined
        );
      }

    } catch (error) {
      console.error('Error sending invitation email:', error);
      throw error;
    }
  }

  /**
   * Fallback invitation email when database template is not available
   */
  private async sendInvitationEmailFallback(
    to: string,
    organizationName: string,
    inviterName: string,
    role: string,
    invitationLink: string,
    expiryDate: string,
    message?: string
  ): Promise<void> {
    const subject = `You're invited to join ${organizationName} on DeepLedger`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to ${organizationName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .role-badge { background: #e0f2fe; color: #0369a1; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 You're Invited!</h1>
            <p>Join ${organizationName} on DeepLedger</p>
          </div>
          <div class="content">
            <p>Hello!</p>
            <p><strong>${inviterName}</strong> has invited you to join <strong>${organizationName}</strong> as a <span class="role-badge">${role}</span>.</p>

            ${message ? `<div style="background: #fff; padding: 15px; border-left: 4px solid #2563eb; margin: 20px 0;"><p><strong>Personal message:</strong></p><p style="font-style: italic;">"${message}"</p></div>` : ''}

            <p><strong>Role Description:</strong><br>${this.getRoleDescription(role)}</p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${invitationLink}" class="button">Accept Invitation</a>
            </div>

            <p style="font-size: 14px; color: #666;">
              This invitation will expire on <strong>${expiryDate}</strong>.
            </p>

            <p style="font-size: 14px; color: #666;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${invitationLink}">${invitationLink}</a>
            </p>
          </div>
          <div class="footer">
            <p>This email was sent by DeepLedger on behalf of ${organizationName}</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
You're invited to join ${organizationName} on DeepLedger!

${inviterName} has invited you to join ${organizationName} as a ${role}.

${message ? `Personal message: "${message}"` : ''}

Role: ${role}
${this.getRoleDescription(role)}

Accept your invitation: ${invitationLink}

This invitation expires on ${expiryDate}.

---
This email was sent by DeepLedger on behalf of ${organizationName}.
If you didn't expect this invitation, you can safely ignore this email.
    `;

    await this.sendRealEmail({
      to,
      subject,
      html,
      text,
      fromEmail: process.env.SENDGRID_FROM_EMAIL,
      fromName: 'DeepLedger',
      invitationLink
    });
  }

  /**
   * Send real email using SendGrid
   */
  private async sendRealEmail({
    to,
    subject,
    html,
    text,
    fromEmail,
    fromName,
    invitationLink
  }: {
    to: string;
    subject: string;
    html: string;
    text?: string;
    fromEmail?: string;
    fromName?: string;
    invitationLink?: string;
  }): Promise<void> {
    try {
      const defaultFromEmail = process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
      const defaultFromName = 'DeepLedger';

      const msg = {
        to,
        from: {
          email: fromEmail || defaultFromEmail,
          name: fromName || defaultFromName
        },
        subject,
        text: text || `Email from ${fromName || defaultFromName}. ${invitationLink ? `Visit: ${invitationLink}` : ''}`,
        html,
      };

      console.log('📧 Sending real email via SendGrid...');
      console.log('To:', to);
      console.log('From:', msg.from.email);
      console.log('Subject:', subject);

      const response = await sgMail.send(msg);

      console.log('✅ Email sent successfully via SendGrid!');
      console.log('📧 Email ID:', response[0].headers['x-message-id']);
      if (invitationLink) {
        console.log('🔗 Invitation Link:', invitationLink);
      }

    } catch (error: any) {
      console.error('❌ SendGrid email sending failed:', error);

      if (error.response) {
        console.error('SendGrid Error Details:', error.response.body);
      }

      // Fall back to simulation if SendGrid fails
      console.log('📧 Falling back to email simulation...');
      await this.simulateEmailSending(to, subject, invitationLink || 'N/A');
    }
  }

  /**
   * Simulate email sending for development
   */
  private async simulateEmailSending(
    to: string,
    subject: string,
    invitationLink: string
  ): Promise<void> {
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log(`
    ═══════════════════════════════════════════════════════════════
    📧 EMAIL SENT (SIMULATED)
    ═══════════════════════════════════════════════════════════════
    To: ${to}
    Subject: ${subject}

    🔗 Invitation Link: ${invitationLink}

    ⏰ This invitation will expire in 7 days.
    ═══════════════════════════════════════════════════════════════
    `);
  }

  /**
   * Send welcome email after user accepts invitation
   */
  async sendWelcomeEmail(
    userEmail: string,
    userName: string,
    organizationName: string,
    role: string,
    dashboardUrl: string = 'http://localhost:5174/dashboard'
  ): Promise<void> {
    try {
      const variables = {
        organization_name: organizationName,
        user_name: userName,
        role: role,
        role_description: this.getRoleDescription(role),
        dashboard_link: dashboardUrl
      };

      // Send welcome email using generic template method
      await this.sendTemplatedEmail(
        'welcome_email',
        userEmail,
        variables,
        {
          fromEmail: process.env.SENDGRID_FROM_EMAIL,
          fromName: 'DeepLedger Team'
        }
      );

    } catch (error) {
      console.error('Error sending welcome email:', error);
      // Don't throw error for welcome email failures - log and continue
    }
  }

  /**
   * Validate email address format
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate verification token (URL-safe)
   */
  static generateVerificationToken(): string {
    // Use URL-safe characters only (no special characters that might cause issues)
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // Add timestamp prefix for debugging (optional)
    const timestamp = Date.now().toString(36);
    return `${timestamp}_${result}`;
  }
}
