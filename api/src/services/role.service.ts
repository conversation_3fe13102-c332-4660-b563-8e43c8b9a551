import { RoleRepository } from "../repositories/role.repository";
import { Role, CreateRoleRequest, UpdateRoleRequest, RoleName, ROLE_HIERARCHY } from "../models/role.model";

export class RoleService {
  private roleRepository: RoleRepository;

  constructor() {
    this.roleRepository = new RoleRepository();
  }

  /**
   * Get all available roles
   */
  async getAllRoles(): Promise<Role[]> {
    try {
      return await this.roleRepository.findAll();
    } catch (error) {
      console.error("Error in getAllRoles:", error);
      throw error;
    }
  }

  /**
   * Get system roles only (predefined roles)
   */
  async getSystemRoles(): Promise<Role[]> {
    try {
      return await this.roleRepository.findSystemRoles();
    } catch (error) {
      console.error("Error in getSystemRoles:", error);
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  async getRoleById(roleId: string): Promise<Role | null> {
    try {
      return await this.roleRepository.findById(roleId);
    } catch (error) {
      console.error("Error in getRoleById:", error);
      throw error;
    }
  }

  /**
   * Get role by name
   */
  async getRoleByName(name: string): Promise<Role | null> {
    try {
      return await this.roleRepository.findByName(name);
    } catch (error) {
      console.error("Error in getRoleByName:", error);
      throw error;
    }
  }

  /**
   * Create a custom role
   */
  async createRole(roleData: CreateRoleRequest): Promise<Role> {
    try {
      // Validate that the role name doesn't conflict with system roles
      const existingRole = await this.roleRepository.findByName(roleData.name);
      if (existingRole) {
        throw new Error("Role name already exists");
      }

      // Ensure custom roles are not marked as system roles
      const customRoleData = {
        ...roleData,
        is_system_role: false,
      };

      return await this.roleRepository.create(customRoleData);
    } catch (error) {
      console.error("Error in createRole:", error);
      throw error;
    }
  }

  /**
   * Update a custom role
   */
  async updateRole(roleId: string, updateData: UpdateRoleRequest): Promise<Role> {
    try {
      const existingRole = await this.roleRepository.findById(roleId);
      if (!existingRole) {
        throw new Error("Role not found");
      }

      if (existingRole.is_system_role) {
        throw new Error("Cannot update system roles");
      }

      return await this.roleRepository.update(roleId, updateData);
    } catch (error) {
      console.error("Error in updateRole:", error);
      throw error;
    }
  }

  /**
   * Delete a custom role
   */
  async deleteRole(roleId: string): Promise<void> {
    try {
      const existingRole = await this.roleRepository.findById(roleId);
      if (!existingRole) {
        throw new Error("Role not found");
      }

      if (existingRole.is_system_role) {
        throw new Error("Cannot delete system roles");
      }

      await this.roleRepository.delete(roleId);
    } catch (error) {
      console.error("Error in deleteRole:", error);
      throw error;
    }
  }

  /**
   * Check if a role has a specific permission
   */
  hasPermission(role: Role, permission: string): boolean {
    return role.permissions.includes(permission);
  }

  /**
   * Check if one role has higher hierarchy than another
   */
  isHigherRole(role1Name: string, role2Name: string): boolean {
    const hierarchy1 = ROLE_HIERARCHY[role1Name as RoleName] || 0;
    const hierarchy2 = ROLE_HIERARCHY[role2Name as RoleName] || 0;
    return hierarchy1 > hierarchy2;
  }

  /**
   * Get roles that can be assigned by a user with a specific role
   */
  async getAssignableRoles(userRoleName: string): Promise<Role[]> {
    try {
      const allRoles = await this.getSystemRoles();
      const userHierarchy = ROLE_HIERARCHY[userRoleName as RoleName] || 0;

      // Users can only assign roles with lower or equal hierarchy
      // Owners can assign any role, admins can assign admin/accountant/member/viewer, etc.
      return allRoles.filter(role => {
        const roleHierarchy = ROLE_HIERARCHY[role.name as RoleName] || 0;
        return roleHierarchy <= userHierarchy;
      });
    } catch (error) {
      console.error("Error in getAssignableRoles:", error);
      throw error;
    }
  }

  /**
   * Validate if a user can assign a specific role
   */
  async canAssignRole(userRoleName: string, targetRoleName: string): Promise<boolean> {
    try {
      const assignableRoles = await this.getAssignableRoles(userRoleName);
      return assignableRoles.some(role => role.name === targetRoleName);
    } catch (error) {
      console.error("Error in canAssignRole:", error);
      return false;
    }
  }
}
