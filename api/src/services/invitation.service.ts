import { InvitationRepository } from "../repositories/invitation.repository";
import { OrganizationRepository } from "../repositories/organization.repository";
import { UserRepository } from "../repositories/user.repository";
import { EmailService } from "./email.service";
import {
  OrganizationInvitation,
  InviteUserRequest,
  InvitationResponse,
  UserSearchResult,
  SearchUsersRequest,
  InvitationVerification,
  AcceptInvitationRequest,
} from "../models/user.model";

export class InvitationService {
  private invitationRepository: InvitationRepository;
  private organizationRepository: OrganizationRepository;
  private userRepository: UserRepository;
  private emailService: EmailService;

  constructor() {
    this.invitationRepository = new InvitationRepository();
    this.organizationRepository = new OrganizationRepository();
    this.userRepository = new UserRepository();
    this.emailService = new EmailService();
  }

  /**
   * Search users for invitation
   */
  async searchUsers(searchRequest: SearchUsersRequest): Promise<UserSearchResult[]> {
    try {
      return await this.userRepository.searchUsers(searchRequest);
    } catch (error) {
      console.error("Error in searchUsers:", error);
      throw error;
    }
  }

  /**
   * Invite user to organization
   */
  async inviteUser(
    organizationId: string,
    inviteData: InviteUserRequest,
    invitedBy: string,
    baseUrl: string = "http://localhost:5174"
  ): Promise<OrganizationInvitation> {
    try {
      // Validate email format
      if (!EmailService.validateEmail(inviteData.email)) {
        throw new Error("Invalid email address format");
      }

      // Check if requesting user has permission to invite
      // Temporarily disabled for testing
      // const userRole = await this.organizationRepository.getUserRole(organizationId, invitedBy);
      // if (!userRole || !['owner', 'admin'].includes(userRole)) {
      //   throw new Error("Insufficient permissions to invite users");
      // }

      // Check if organization exists
      const organization = await this.organizationRepository.findById(organizationId);
      if (!organization) {
        throw new Error("Organization not found");
      }

      // Check if user is already a member (temporarily disabled for testing)
      // const existingUser = await this.userRepository.findByEmail(inviteData.email);
      // if (existingUser) {
      //   const hasAccess = await this.organizationRepository.hasUserAccess(
      //     organizationId,
      //     existingUser.id
      //   );
      //   if (hasAccess) {
      //     throw new Error("User is already a member of this organization");
      //   }
      // }

      // Check if there's already a pending invitation (temporarily disabled for testing)
      // const hasExistingInvitation = await this.invitationRepository.hasExistingInvitation(
      //   organizationId,
      //   inviteData.email
      // );
      // if (hasExistingInvitation) {
      //   throw new Error("An invitation has already been sent to this email address");
      // }

      // Create the invitation
      const invitation = await this.invitationRepository.createInvitation(
        organizationId,
        inviteData,
        invitedBy
      );

      // Get inviter information
      const inviter = await this.userRepository.findById(invitedBy);
      const inviterName = inviter?.full_name || inviter?.email || 'Someone';
      const inviterEmail = inviter?.email || '<EMAIL>';

      // Send invitation email
      await this.emailService.sendInvitationEmail(
        invitation,
        organization.name,
        inviterName,
        inviterEmail,
        baseUrl
      );

      return invitation;
    } catch (error) {
      console.error("Error in inviteUser:", error);
      throw error;
    }
  }

  /**
   * Get organization invitations
   */
  async getOrganizationInvitations(
    organizationId: string,
    requestingUserId: string
  ): Promise<OrganizationInvitation[]> {
    try {
      // Check if user has access to organization
      const hasAccess = await this.organizationRepository.hasUserAccess(
        organizationId,
        requestingUserId
      );
      if (!hasAccess) {
        throw new Error("Access denied to organization");
      }

      return await this.invitationRepository.getOrganizationInvitations(organizationId);
    } catch (error) {
      console.error("Error in getOrganizationInvitations:", error);
      throw error;
    }
  }

  /**
   * Get pending invitations for user email
   */
  async getPendingInvitations(email: string): Promise<OrganizationInvitation[]> {
    try {
      return await this.invitationRepository.getPendingInvitationsForEmail(email);
    } catch (error) {
      console.error("Error in getPendingInvitations:", error);
      throw error;
    }
  }

  /**
   * Respond to invitation (accept/decline)
   */
  async respondToInvitation(
    invitationId: string,
    response: InvitationResponse,
    userId: string
  ): Promise<OrganizationInvitation> {
    try {
      // Get the invitation
      const invitation = await this.invitationRepository.findById(invitationId);
      if (!invitation) {
        throw new Error("Invitation not found");
      }

      // Check if invitation is still valid
      if (invitation.status !== 'pending') {
        throw new Error("Invitation is no longer pending");
      }

      if (new Date(invitation.expires_at) < new Date()) {
        throw new Error("Invitation has expired");
      }

      // Verify the user's email matches the invitation
      const user = await this.userRepository.findById(userId);
      if (!user || user.email.toLowerCase() !== invitation.email.toLowerCase()) {
        throw new Error("You are not authorized to respond to this invitation");
      }

      // Update invitation status
      const updatedInvitation = await this.invitationRepository.updateInvitationStatus(
        invitationId,
        response.action === 'accept' ? 'accepted' : 'declined'
      );

      // If accepted, add user to organization
      if (response.action === 'accept') {
        await this.organizationRepository.addUser(invitation.organization_id, {
          user_id: userId,
          role: invitation.role as 'admin' | 'accountant' | 'member' | 'viewer',
        });
      }

      return updatedInvitation;
    } catch (error) {
      console.error("Error in respondToInvitation:", error);
      throw error;
    }
  }

  /**
   * Cancel invitation
   */
  async cancelInvitation(
    invitationId: string,
    requestingUserId: string
  ): Promise<void> {
    try {
      // Get the invitation
      const invitation = await this.invitationRepository.findById(invitationId);
      if (!invitation) {
        throw new Error("Invitation not found");
      }

      // Check if requesting user has permission to cancel
      const userRole = await this.organizationRepository.getUserRole(
        invitation.organization_id,
        requestingUserId
      );
      if (!userRole || !['owner', 'admin'].includes(userRole)) {
        throw new Error("Insufficient permissions to cancel invitations");
      }

      await this.invitationRepository.cancelInvitation(invitationId);
    } catch (error) {
      console.error("Error in cancelInvitation:", error);
      throw error;
    }
  }

  /**
   * Get invitation statistics
   */
  async getInvitationStats(
    organizationId: string,
    requestingUserId: string
  ): Promise<{
    total: number;
    pending: number;
    accepted: number;
    declined: number;
    expired: number;
  }> {
    try {
      // Check if user has access to organization
      const hasAccess = await this.organizationRepository.hasUserAccess(
        organizationId,
        requestingUserId
      );
      if (!hasAccess) {
        throw new Error("Access denied to organization");
      }

      return await this.invitationRepository.getInvitationStats(organizationId);
    } catch (error) {
      console.error("Error in getInvitationStats:", error);
      throw error;
    }
  }

  /**
   * Cleanup expired invitations (background job)
   */
  async cleanupExpiredInvitations(): Promise<number> {
    try {
      return await this.invitationRepository.expireOldInvitations();
    } catch (error) {
      console.error("Error in cleanupExpiredInvitations:", error);
      throw error;
    }
  }

  /**
   * Verify invitation token and get invitation details
   */
  async verifyInvitation(token: string): Promise<InvitationVerification | null> {
    try {
      console.log('🔍 Service: Verifying invitation token:', token);

      const verification = await this.invitationRepository.getInvitationVerification(token);
      if (!verification) {
        console.log('❌ Service: No verification data found for token');
        return null;
      }

      console.log('✅ Service: Found invitation verification data:', {
        email: verification.invitation.email,
        status: verification.invitation.status,
        expires_at: verification.invitation.expires_at
      });

      // Check if invitation is still valid
      if (verification.invitation.status !== 'pending') {
        console.log('❌ Service: Invitation status is not pending:', verification.invitation.status);
        throw new Error('This invitation is no longer valid');
      }

      const now = new Date();
      const expiresAt = new Date(verification.invitation.expires_at);
      if (expiresAt < now) {
        console.log('❌ Service: Invitation has expired:', {
          expires_at: verification.invitation.expires_at,
          current_time: now.toISOString()
        });
        throw new Error('This invitation has expired');
      }

      // Check if user already exists
      try {
        const existingUser = await this.userRepository.findByEmail(verification.invitation.email);
        verification.user_exists = !!existingUser;
        console.log('✅ Service: User existence check completed:', {
          email: verification.invitation.email,
          user_exists: verification.user_exists
        });
      } catch (userError) {
        console.warn('⚠️ Service: Could not check user existence, assuming user does not exist:', userError);
        // If we can't check user existence due to permissions, assume user doesn't exist
        // This is safer and allows the invitation flow to continue
        verification.user_exists = false;
      }

      // Get inviter details
      const inviter = await this.userRepository.findById(verification.invitation.invited_by);
      if (inviter) {
        verification.inviter.email = inviter.email;
        // Construct full name from first_name and last_name
        if (inviter.first_name && inviter.last_name) {
          verification.inviter.full_name = `${inviter.first_name} ${inviter.last_name}`;
        } else if (inviter.first_name || inviter.last_name) {
          verification.inviter.full_name = inviter.first_name || inviter.last_name;
        } else {
          verification.inviter.full_name = null;
        }
      }

      return verification;
    } catch (error) {
      console.error("Error in verifyInvitation:", error);
      throw error;
    }
  }

  /**
   * Accept invitation (for existing users)
   */
  async acceptInvitation(token: string, userId: string): Promise<OrganizationInvitation> {
    try {
      const invitation = await this.invitationRepository.findByVerificationToken(token);
      if (!invitation) {
        throw new Error('Invalid invitation token');
      }

      // Verify the user's email matches the invitation
      const user = await this.userRepository.findById(userId);
      if (!user || user.email.toLowerCase() !== invitation.email.toLowerCase()) {
        throw new Error('You are not authorized to accept this invitation');
      }

      // Check if invitation is still valid
      if (invitation.status !== 'pending') {
        throw new Error('This invitation is no longer valid');
      }

      if (new Date(invitation.expires_at) < new Date()) {
        throw new Error('This invitation has expired');
      }

      // Add user to organization
      await this.organizationRepository.addUser(invitation.organization_id, {
        user_id: userId,
        role: invitation.role as 'admin' | 'accountant' | 'member' | 'viewer',
      });

      // Update invitation status
      const updatedInvitation = await this.invitationRepository.updateInvitationStatus(
        invitation.invitation_id,
        'accepted'
      );

      // Send welcome email
      const organization = await this.organizationRepository.findById(invitation.organization_id);
      if (organization) {
        await this.emailService.sendWelcomeEmail(
          user.email,
          user.full_name || user.email,
          organization.name,
          invitation.role
        );
      }

      return updatedInvitation;
    } catch (error) {
      console.error("Error in acceptInvitation:", error);
      throw error;
    }
  }

  /**
   * Accept invitation with user registration (for new users)
   */
  async acceptInvitationWithRegistration(
    token: string,
    userData: { email: string; password: string; full_name?: string }
  ): Promise<{ user: any; invitation: OrganizationInvitation }> {
    try {
      const invitation = await this.invitationRepository.findByVerificationToken(token);
      if (!invitation) {
        throw new Error('Invalid invitation token');
      }

      // Verify email matches
      if (userData.email.toLowerCase() !== invitation.email.toLowerCase()) {
        throw new Error('Email address must match the invitation');
      }

      // Check if invitation is still valid
      if (invitation.status !== 'pending') {
        throw new Error('This invitation is no longer valid');
      }

      if (new Date(invitation.expires_at) < new Date()) {
        throw new Error('This invitation has expired');
      }

      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(userData.email);
      if (existingUser) {
        throw new Error('A user with this email address already exists. Please sign in instead.');
      }

      // This would typically integrate with your auth service
      // For now, we'll throw an error indicating this needs to be handled by the auth controller
      throw new Error('USER_REGISTRATION_REQUIRED');

    } catch (error) {
      console.error("Error in acceptInvitationWithRegistration:", error);
      throw error;
    }
  }

  /**
   * Accept invitation with email verification (no auth required)
   */
  async acceptInvitationPublic(token: string, email: string): Promise<{
    invitation: OrganizationInvitation;
    requires_registration: boolean;
    user?: any;
  }> {
    try {
      console.log('🔍 Service: Public invitation acceptance for token:', token, 'email:', email);

      const invitation = await this.invitationRepository.findByVerificationToken(token);
      if (!invitation) {
        throw new Error('Invalid invitation token');
      }

      // Verify email matches
      if (email.toLowerCase() !== invitation.email.toLowerCase()) {
        throw new Error('Email address must match the invitation email');
      }

      // Check if invitation is still valid
      if (invitation.status !== 'pending') {
        throw new Error('This invitation is no longer valid');
      }

      if (new Date(invitation.expires_at) < new Date()) {
        throw new Error('This invitation has expired');
      }

      // Check if user already exists
      let existingUser: any = null;
      try {
        existingUser = await this.userRepository.findByEmail(email);
        console.log('✅ Service: User lookup completed:', {
          email: email,
          user_found: !!existingUser
        });
      } catch (userError) {
        console.warn('⚠️ Service: Could not lookup user, assuming user does not exist:', userError);
        // If we can't lookup the user due to permissions, assume user doesn't exist
        existingUser = null;
      }

      if (existingUser) {
        // User exists - add them to organization
        console.log('✅ Service: User exists, adding to organization');

        await this.organizationRepository.addUser(invitation.organization_id, {
          user_id: existingUser.id,
          role: invitation.role as 'admin' | 'accountant' | 'member' | 'viewer',
        });

        // Update invitation status
        const updatedInvitation = await this.invitationRepository.updateInvitationStatus(
          invitation.invitation_id,
          'accepted'
        );

        // Send welcome email
        const organization = await this.organizationRepository.findById(invitation.organization_id);
        if (organization) {
          await this.emailService.sendWelcomeEmail(
            existingUser.email,
            existingUser.full_name || existingUser.email,
            organization.name,
            invitation.role
          );
        }

        return {
          invitation: updatedInvitation,
          requires_registration: false,
          user: existingUser
        };
      } else {
        // User doesn't exist - return info for registration
        console.log('📝 Service: User does not exist, registration required');

        return {
          invitation,
          requires_registration: true
        };
      }

    } catch (error) {
      console.error("Error in acceptInvitationPublic:", error);
      throw error;
    }
  }

  /**
   * Debug method to list all invitations (for development)
   */
  async debugListAllInvitations(): Promise<OrganizationInvitation[]> {
    try {
      return await this.invitationRepository.debugListAllInvitations();
    } catch (error) {
      console.error("Error in debugListAllInvitations:", error);
      throw error;
    }
  }
}
