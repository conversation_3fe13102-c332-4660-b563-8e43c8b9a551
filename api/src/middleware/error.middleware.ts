import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "../models/user.model";
import { env } from "../config/env";

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

/**
 * Custom error class for application errors
 */
export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error class
 */
export class ValidationError extends CustomError {
  public errors: Record<string, string[]>;

  constructor(message: string, errors: Record<string, string[]> = {}) {
    super(message, 400);
    this.errors = errors;
  }
}

/**
 * Not found error class
 */
export class NotFoundError extends CustomError {
  constructor(message: string = "Resource not found") {
    super(message, 404);
  }
}

/**
 * Unauthorized error class
 */
export class UnauthorizedError extends CustomError {
  constructor(message: string = "Unauthorized") {
    super(message, 401);
  }
}

/**
 * Forbidden error class
 */
export class ForbiddenError extends CustomError {
  constructor(message: string = "Forbidden") {
    super(message, 403);
  }
}

/**
 * Conflict error class
 */
export class ConflictError extends CustomError {
  constructor(message: string = "Conflict") {
    super(message, 409);
  }
}

/**
 * Global error handling middleware
 */
export const errorMiddleware = (
  error: AppError,
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  let statusCode = error.statusCode || 500;
  let message = error.message || "Internal Server Error";

  // Log error
  if (req.logger) {
    req.logger.error("Error occurred", {
      error: message,
      stack: error.stack,
      statusCode,
      url: req.url,
      method: req.method,
    });
  } else {
    console.error("Error occurred:", {
      error: message,
      stack: error.stack,
      statusCode,
      url: req.url,
      method: req.method,
    });
  }

  // Handle specific error types
  if (error.name === "ValidationError") {
    statusCode = 400;
    message = "Validation failed";
  } else if (error.name === "CastError") {
    statusCode = 400;
    message = "Invalid ID format";
  } else if (error.name === "JsonWebTokenError") {
    statusCode = 401;
    message = "Invalid token";
  } else if (error.name === "TokenExpiredError") {
    statusCode = 401;
    message = "Token expired";
  }

  // Prepare error response
  const errorResponse: ApiResponse = {
    success: false,
    error: message,
  };

  // Add validation errors if present
  if (error instanceof ValidationError && error.errors) {
    errorResponse.errors = error.errors;
  }

  // Add stack trace in development
  if (env.NODE_ENV === "development") {
    (errorResponse as any).stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 Not Found middleware
 */
export const notFoundMiddleware = (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Async error handler wrapper
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Create error response helper
 */
export const createErrorResponse = (
  message: string,
  statusCode: number = 500,
  errors?: Record<string, string[]>
): ApiResponse => {
  const response: ApiResponse = {
    success: false,
    error: message,
  };

  if (errors) {
    response.errors = errors;
  }

  return response;
};

export default errorMiddleware;
