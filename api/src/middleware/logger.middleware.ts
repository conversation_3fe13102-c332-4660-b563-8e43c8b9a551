import { Request, Response, NextFunction } from "express";
import winston from "winston";
import { env } from "../config/env";

// Create Winston logger
const logger = winston.createLogger({
  level: env.LOG_LEVEL,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: "deepledger-api" },
  transports: [
    new winston.transports.File({ filename: "logs/error.log", level: "error" }),
    new winston.transports.File({ filename: "logs/combined.log" }),
  ],
});

// Add console transport for non-production environments
if (env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  );
}

// Extend Request interface to include logger
declare global {
  namespace Express {
    interface Request {
      logger: winston.Logger;
      requestId: string;
    }
  }
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

/**
 * Logger middleware
 */
export const loggerMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const requestId = generateRequestId();
  req.requestId = requestId;

  // Create child logger with request context
  req.logger = logger.child({
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get("User-Agent"),
    ip: req.ip,
  });

  // Log incoming request
  req.logger.info("Incoming request", {
    method: req.method,
    url: req.url,
    query: req.query,
    body: req.method !== "GET" ? sanitizeBody(req.body) : undefined,
  });

  // Capture response details
  const originalSend = res.send;
  res.send = function (body: any) {
    req.logger.info("Outgoing response", {
      statusCode: res.statusCode,
      responseTime: Date.now() - req.startTime,
    });
    return originalSend.call(this, body);
  };

  // Add start time for response time calculation
  req.startTime = Date.now();

  next();
};

/**
 * Sanitize request body for logging (remove sensitive data)
 */
function sanitizeBody(body: any): any {
  if (!body || typeof body !== "object") {
    return body;
  }

  const sensitiveFields = [
    "password",
    "token",
    "secret",
    "key",
    "authorization",
  ];
  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = "[REDACTED]";
    }
  }

  return sanitized;
}

/**
 * Error logging middleware
 */
export const errorLoggerMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  req.logger.error("Request error", {
    error: error.message,
    stack: error.stack,
    method: req.method,
    url: req.url,
  });

  next(error);
};

// Extend Request interface for startTime
declare global {
  namespace Express {
    interface Request {
      startTime: number;
    }
  }
}

export { logger };
export default loggerMiddleware;
