import { Request, Response, NextFunction } from "express";
import { JwtUtils } from "../utils/jwt.utils";
import { UserRepository } from "../repositories/user.repository";
import { AuthenticatedUser, ApiResponse } from "../models/user.model";

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
    }
  }
}

/**
 * Authentication middleware
 * Validates JWT token and attaches user to request
 */
export const authMiddleware = async (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      res.status(401).json({
        success: false,
        error: "Authorization header is required",
      });
      return;
    }

    const token = authHeader.startsWith("Bearer ")
      ? authHeader.substring(7)
      : authHeader;

    if (!token) {
      res.status(401).json({
        success: false,
        error: "Access token is required",
      });
      return;
    }

    // Verify JWT token
    let payload;
    try {
      payload = JwtUtils.verifyToken(token);
    } catch (error) {
      res.status(401).json({
        success: false,
        error: error instanceof Error ? error.message : "Invalid token",
      });
      return;
    }

    // Get user from database
    const userRepository = new UserRepository();
    const user = await userRepository.findById(payload.sub);

    if (!user) {
      res.status(401).json({
        success: false,
        error: "User not found",
      });
      return;
    }

    // Attach user to request
    req.user = {
      id: user.id,
      email: user.email,
      full_name: user.full_name,
    };

    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    res.status(500).json({
      success: false,
      error: "Internal server error",
    });
  }
};

/**
 * Optional authentication middleware
 * Validates JWT token if present but doesn't require it
 */
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      next();
      return;
    }

    const token = authHeader.startsWith("Bearer ")
      ? authHeader.substring(7)
      : authHeader;

    if (!token) {
      next();
      return;
    }

    try {
      // Verify JWT token
      const payload = JwtUtils.verifyToken(token);

      // Get user from database
      const userRepository = new UserRepository();
      const user = await userRepository.findById(payload.sub);

      if (user) {
        // Attach user to request if found
        req.user = {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
        };
      }
    } catch (error) {
      // Ignore token errors in optional auth
      console.warn("Optional auth token error:", error);
    }

    next();
  } catch (error) {
    console.error("Optional auth middleware error:", error);
    next();
  }
};

/**
 * Role-based authorization middleware
 * Requires specific roles (can be extended for role-based access)
 */
export const requireRole = (roles: string[]) => {
  return async (
    req: Request,
    res: Response<ApiResponse>,
    next: NextFunction
  ): Promise<void> => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: "Authentication required",
      });
      return;
    }

    // For now, we don't have roles implemented
    // This is a placeholder for future role-based authorization
    // You can extend the User model to include roles and implement this logic

    next();
  };
};

/**
 * Admin authorization middleware
 */
export const requireAdmin = requireRole(["admin"]);

export default authMiddleware;
