export interface Role {
  role_id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  is_system_role: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateRoleRequest {
  name: string;
  display_name: string;
  description?: string;
  permissions?: string[];
  is_system_role?: boolean;
}

export interface UpdateRoleRequest {
  display_name?: string;
  description?: string;
  permissions?: string[];
}

export type RoleName = 'owner' | 'admin' | 'accountant' | 'member' | 'viewer';

export const ROLE_PERMISSIONS = {
  owner: ['read', 'write', 'admin', 'owner'],
  admin: ['read', 'write', 'admin'],
  accountant: ['read', 'write', 'financial'],
  member: ['read', 'write'],
  viewer: ['read'],
} as const;

export const ROLE_HIERARCHY = {
  owner: 5,
  admin: 4,
  accountant: 3,
  member: 2,
  viewer: 1,
} as const;
