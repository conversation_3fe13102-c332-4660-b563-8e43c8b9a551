export interface User {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null; // Computed field from first_name + last_name
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  full_name?: string;
  first_name?: string;
  last_name?: string;
}

export interface UpdateUserRequest {
  full_name?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ConfirmResetPasswordRequest {
  token: string;
  new_password: string;
}

export interface UserProfile {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null; // Computed field
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// JWT Payload
export interface JwtPayload {
  sub: string; // user id
  email: string;
  iat: number;
  exp: number;
}

export interface AuthenticatedUser {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null;
}

// Organization Models
export interface Organization {
  organization_id: string;
  name: string;
  slug: string;
  logo?: string | null;
  settings?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: string | null;
  updated_by?: string | null;
}

export interface OrganizationUser {
  organization_user_id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'accountant' | 'member' | 'viewer';
  role_id?: string; // For future migration to role table references
  created_at: string;
  updated_at: string;
}

export interface CreateOrganizationRequest {
  name: string;
  slug?: string;
  logo?: string;
  settings?: Record<string, any>;
}

export interface UpdateOrganizationRequest {
  name?: string;
  slug?: string;
  logo?: string;
  settings?: Record<string, any>;
}

export interface AddOrganizationUserRequest {
  user_id: string;
  role: 'admin' | 'accountant' | 'member' | 'viewer';
  role_id?: string; // For future migration to role table references
}

export interface UpdateOrganizationUserRequest {
  role: 'admin' | 'accountant' | 'member' | 'viewer';
  role_id?: string; // For future migration to role table references
}

export interface OrganizationWithRole extends Organization {
  user_role: string;
}

export interface OrganizationMember {
  organization_user_id: string;
  user_id: string;
  role: string;
  created_at: string;
  updated_at: string;
  user: {
    email: string;
    full_name?: string | null;
    avatar_url?: string | null;
  };
}

// User Search and Invitation Models
export interface UserSearchResult {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  is_member?: boolean; // Whether user is already a member of the organization
}

export interface SearchUsersRequest {
  query: string;
  organization_id?: string; // To check membership status
  limit?: number;
}

export interface InviteUserRequest {
  email: string;
  role: 'admin' | 'accountant' | 'member' | 'viewer';
  role_id?: string; // For future migration to role table references
  message?: string;
}

export interface OrganizationInvitation {
  invitation_id: string;
  organization_id: string;
  email: string;
  role: 'admin' | 'accountant' | 'member' | 'viewer';
  role_id?: string; // For future migration to role table references
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  message?: string | null;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  responded_at?: string | null;
  verification_token?: string;
}

export interface InvitationResponse {
  invitation_id: string;
  action: 'accept' | 'decline';
}

export interface InvitationVerification {
  invitation: OrganizationInvitation;
  organization: {
    organization_id: string;
    name: string;
    logo?: string | null;
  };
  inviter: {
    full_name?: string | null;
    email: string;
  };
  user_exists: boolean;
}

export interface AcceptInvitationRequest {
  token: string;
  user_data?: {
    email: string;
    password: string;
    full_name?: string;
  };
}
