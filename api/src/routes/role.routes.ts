import { Router } from "express";
import { <PERSON><PERSON><PERSON>roller } from "../controllers/role.controller";
import { authMiddleware } from "../middleware/auth.middleware";

const router = Router();
const roleController = new RoleController();

/**
 * @swagger
 * components:
 *   schemas:
 *     Role:
 *       type: object
 *       properties:
 *         role_id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         display_name:
 *           type: string
 *         description:
 *           type: string
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *         is_system_role:
 *           type: boolean
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CreateRoleRequest:
 *       type: object
 *       required:
 *         - name
 *         - display_name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 50
 *         display_name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         description:
 *           type: string
 *           maxLength: 500
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *     UpdateRoleRequest:
 *       type: object
 *       properties:
 *         display_name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         description:
 *           type: string
 *           maxLength: 500
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 */

/**
 * @swagger
 * /api/roles:
 *   get:
 *     summary: Get all roles
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 */
router.get("/", authMiddleware, roleController.getAllRoles);

/**
 * @swagger
 * /api/roles/system:
 *   get:
 *     summary: Get system roles only
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 */
router.get("/system", authMiddleware, roleController.getSystemRoles);

/**
 * @swagger
 * /api/roles/assignable:
 *   get:
 *     summary: Get roles that can be assigned by current user
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userRole
 *         required: true
 *         schema:
 *           type: string
 *         description: Current user's role
 *     responses:
 *       200:
 *         description: Assignable roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       400:
 *         description: User role is required
 *       401:
 *         description: Unauthorized
 */
router.get("/assignable", authMiddleware, roleController.getAssignableRoles);

/**
 * @swagger
 * /api/roles/{roleId}:
 *   get:
 *     summary: Get role by ID
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Role retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       404:
 *         description: Role not found
 *       401:
 *         description: Unauthorized
 */
router.get("/:roleId", authMiddleware, roleController.getRoleById);

/**
 * @swagger
 * /api/roles:
 *   post:
 *     summary: Create a custom role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateRoleRequest'
 *     responses:
 *       201:
 *         description: Role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       409:
 *         description: Role name already exists
 *       401:
 *         description: Unauthorized
 */
router.post("/", authMiddleware, roleController.createRole);

/**
 * @swagger
 * /api/roles/{roleId}:
 *   put:
 *     summary: Update a custom role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateRoleRequest'
 *     responses:
 *       200:
 *         description: Role updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Role'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       403:
 *         description: Cannot update system roles
 *       404:
 *         description: Role not found
 *       401:
 *         description: Unauthorized
 */
router.put("/:roleId", authMiddleware, roleController.updateRole);

/**
 * @swagger
 * /api/roles/{roleId}:
 *   delete:
 *     summary: Delete a custom role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Role deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       403:
 *         description: Cannot delete system roles
 *       404:
 *         description: Role not found
 *       401:
 *         description: Unauthorized
 */
router.delete("/:roleId", authMiddleware, roleController.deleteRole);

export default router;
