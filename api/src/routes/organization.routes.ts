import { Router } from "express";
import { OrganizationController } from "../controllers/organization.controller";
import { authMiddleware } from "../middleware/auth.middleware";

const router = Router();
const organizationController = new OrganizationController();

/**
 * @swagger
 * components:
 *   schemas:
 *     Organization:
 *       type: object
 *       properties:
 *         organization_id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         slug:
 *           type: string
 *         logo:
 *           type: string
 *           nullable: true
 *         settings:
 *           type: object
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         created_by:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         updated_by:
 *           type: string
 *           format: uuid
 *           nullable: true
 *     
 *     OrganizationWithRole:
 *       allOf:
 *         - $ref: '#/components/schemas/Organization'
 *         - type: object
 *           properties:
 *             user_role:
 *               type: string
 *               enum: [owner, admin, member, viewer]
 *     
 *     OrganizationUser:
 *       type: object
 *       properties:
 *         organization_user_id:
 *           type: string
 *           format: uuid
 *         organization_id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         role:
 *           type: string
 *           enum: [owner, admin, member, viewer]
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     
 *     OrganizationMember:
 *       allOf:
 *         - $ref: '#/components/schemas/OrganizationUser'
 *         - type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 email:
 *                   type: string
 *                 full_name:
 *                   type: string
 *                   nullable: true
 *                 avatar_url:
 *                   type: string
 *                   nullable: true
 *     
 *     CreateOrganizationRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *         slug:
 *           type: string
 *           pattern: '^[a-z0-9-]+$'
 *           minLength: 1
 *           maxLength: 50
 *         logo:
 *           type: string
 *           format: uri
 *         settings:
 *           type: object
 *     
 *     UpdateOrganizationRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *         slug:
 *           type: string
 *           pattern: '^[a-z0-9-]+$'
 *           minLength: 1
 *           maxLength: 50
 *         logo:
 *           type: string
 *           format: uri
 *         settings:
 *           type: object
 *     
 *     AddOrganizationUserRequest:
 *       type: object
 *       required:
 *         - user_id
 *         - role
 *       properties:
 *         user_id:
 *           type: string
 *           format: uuid
 *         role:
 *           type: string
 *           enum: [admin, member, viewer]
 *     
 *     UpdateOrganizationUserRequest:
 *       type: object
 *       required:
 *         - role
 *       properties:
 *         role:
 *           type: string
 *           enum: [admin, member, viewer]
 */

/**
 * @swagger
 * /api/organizations:
 *   post:
 *     summary: Create a new organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateOrganizationRequest'
 *     responses:
 *       201:
 *         description: Organization created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Organization'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post("/", authMiddleware, organizationController.createOrganization);

/**
 * @swagger
 * /api/organizations/my:
 *   get:
 *     summary: Get current user's organizations
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User organizations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrganizationWithRole'
 *       401:
 *         description: Unauthorized
 */
router.get("/my", authMiddleware, organizationController.getUserOrganizations);

/**
 * @swagger
 * /api/organizations/{organizationId}:
 *   get:
 *     summary: Get organization by ID
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Organization retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Organization'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Organization not found
 */
router.get("/:organizationId", authMiddleware, organizationController.getOrganization);

/**
 * @swagger
 * /api/organizations/{organizationId}:
 *   put:
 *     summary: Update organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateOrganizationRequest'
 *     responses:
 *       200:
 *         description: Organization updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Organization'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization not found
 */
router.put("/:organizationId", authMiddleware, organizationController.updateOrganization);

/**
 * @swagger
 * /api/organizations/{organizationId}:
 *   delete:
 *     summary: Delete organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Organization deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Only owners can delete organizations
 *       404:
 *         description: Organization not found
 */
router.delete("/:organizationId", authMiddleware, organizationController.deleteOrganization);

/**
 * @swagger
 * /api/organizations/{organizationId}/members:
 *   get:
 *     summary: Get organization members
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Organization members retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrganizationMember'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Organization not found
 */
router.get("/:organizationId/members", authMiddleware, organizationController.getMembers);

/**
 * @swagger
 * /api/organizations/{organizationId}/members/stats:
 *   get:
 *     summary: Get organization member statistics
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Member statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     total_members:
 *                       type: number
 *                     role_breakdown:
 *                       type: object
 *                     recent_joins:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Organization not found
 */
router.get("/:organizationId/members/stats", authMiddleware, organizationController.getMemberStats);

/**
 * @swagger
 * /api/organizations/{organizationId}/members:
 *   post:
 *     summary: Add user to organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AddOrganizationUserRequest'
 *     responses:
 *       201:
 *         description: User added to organization successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationUser'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error or user already in organization
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization not found
 */
router.post("/:organizationId/members", authMiddleware, organizationController.addUser);

/**
 * @swagger
 * /api/organizations/{organizationId}/members/{userId}:
 *   put:
 *     summary: Update user role in organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateOrganizationUserRequest'
 *     responses:
 *       200:
 *         description: User role updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationUser'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization or user not found
 */
router.put("/:organizationId/members/:userId", authMiddleware, organizationController.updateUserRole);

/**
 * @swagger
 * /api/organizations/{organizationId}/members/{userId}:
 *   delete:
 *     summary: Remove user from organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: User removed from organization successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization or user not found
 */
router.delete("/:organizationId/members/:userId", authMiddleware, organizationController.removeUser);

/**
 * @swagger
 * /api/organizations/{organizationId}/leave:
 *   post:
 *     summary: Leave organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Left organization successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Cannot leave as only owner
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Organization not found or not a member
 */
router.post("/:organizationId/leave", authMiddleware, organizationController.leaveOrganization);

export default router;
