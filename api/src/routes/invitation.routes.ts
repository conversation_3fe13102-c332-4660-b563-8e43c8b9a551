import { Router } from "express";
import { Invitation<PERSON><PERSON>roller } from "../controllers/invitation.controller";
import { authMiddleware } from "../middleware/auth.middleware";

const router = Router();
const invitationController = new InvitationController();

/**
 * @swagger
 * components:
 *   schemas:
 *     UserSearchResult:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         first_name:
 *           type: string
 *           nullable: true
 *         last_name:
 *           type: string
 *           nullable: true
 *         full_name:
 *           type: string
 *           nullable: true
 *         avatar_url:
 *           type: string
 *           nullable: true
 *         is_member:
 *           type: boolean
 *           description: Whether user is already a member of the organization
 *
 *     OrganizationInvitation:
 *       type: object
 *       properties:
 *         invitation_id:
 *           type: string
 *           format: uuid
 *         organization_id:
 *           type: string
 *           format: uuid
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [admin, member, viewer]
 *         status:
 *           type: string
 *           enum: [pending, accepted, declined, expired]
 *         message:
 *           type: string
 *           nullable: true
 *         invited_by:
 *           type: string
 *           format: uuid
 *         invited_at:
 *           type: string
 *           format: date-time
 *         expires_at:
 *           type: string
 *           format: date-time
 *         responded_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *
 *     InviteUserRequest:
 *       type: object
 *       required:
 *         - email
 *         - role
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [admin, member, viewer]
 *         message:
 *           type: string
 *           maxLength: 500
 *
 *     InvitationResponse:
 *       type: object
 *       required:
 *         - action
 *       properties:
 *         action:
 *           type: string
 *           enum: [accept, decline]
 */

/**
 * @swagger
 * /api/invitations/search:
 *   get:
 *     summary: Search users for invitation
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: query
 *         required: true
 *         schema:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *         description: Search query (matches email, first name, last name)
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Organization ID to check membership status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Maximum number of results to return
 *     responses:
 *       200:
 *         description: Users found successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/UserSearchResult'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
// Temporarily remove auth for testing
router.get("/search", invitationController.searchUsers);

/**
 * @swagger
 * /api/invitations/my:
 *   get:
 *     summary: Get pending invitations for current user
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pending invitations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrganizationInvitation'
 *       401:
 *         description: Unauthorized
 */
router.get("/my", authMiddleware, invitationController.getPendingInvitations);

/**
 * @swagger
 * /api/invitations/{organizationId}:
 *   post:
 *     summary: Invite user to organization
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InviteUserRequest'
 *     responses:
 *       201:
 *         description: Invitation sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationInvitation'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error or user already invited/member
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Organization not found
 */
// Temporarily remove auth for testing
router.post("/:organizationId", invitationController.inviteUser);

/**
 * @swagger
 * /api/invitations/{organizationId}:
 *   get:
 *     summary: Get organization invitations
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Organization invitations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OrganizationInvitation'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Organization not found
 */
router.get("/:organizationId", authMiddleware, invitationController.getOrganizationInvitations);

/**
 * @swagger
 * /api/invitations/{organizationId}/stats:
 *   get:
 *     summary: Get invitation statistics for organization
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Invitation statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     pending:
 *                       type: integer
 *                     accepted:
 *                       type: integer
 *                     declined:
 *                       type: integer
 *                     expired:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Organization not found
 */
router.get("/:organizationId/stats", authMiddleware, invitationController.getInvitationStats);

/**
 * @swagger
 * /api/invitations/respond/{invitationId}:
 *   post:
 *     summary: Respond to invitation (accept/decline)
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: invitationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InvitationResponse'
 *     responses:
 *       200:
 *         description: Invitation response processed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationInvitation'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error or invitation expired/invalid
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Not authorized to respond to this invitation
 *       404:
 *         description: Invitation not found
 */
router.post("/respond/:invitationId", authMiddleware, invitationController.respondToInvitation);

/**
 * @swagger
 * /api/invitations/cancel/{invitationId}:
 *   delete:
 *     summary: Cancel invitation
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: invitationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Invitation canceled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Invitation not found
 */
router.delete("/cancel/:invitationId", authMiddleware, invitationController.cancelInvitation);

/**
 * @swagger
 * /api/invitations/verify/{token}:
 *   get:
 *     summary: Verify invitation token and get invitation details
 *     tags: [Invitations]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Invitation verification token
 *     responses:
 *       200:
 *         description: Invitation verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     invitation:
 *                       $ref: '#/components/schemas/OrganizationInvitation'
 *                     organization:
 *                       type: object
 *                       properties:
 *                         organization_id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         logo:
 *                           type: string
 *                     inviter:
 *                       type: object
 *                       properties:
 *                         full_name:
 *                           type: string
 *                         email:
 *                           type: string
 *                     user_exists:
 *                       type: boolean
 *       404:
 *         description: Invalid or expired invitation
 *       400:
 *         description: Invalid token format
 */
router.get("/verify/:token", invitationController.verifyInvitation);

/**
 * @swagger
 * /api/invitations/accept/{token}:
 *   post:
 *     summary: Accept invitation (for existing users)
 *     tags: [Invitations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Invitation verification token
 *     responses:
 *       200:
 *         description: Invitation accepted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/OrganizationInvitation'
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid token or user not authorized
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Invalid or expired invitation
 */
router.post("/accept/:token", authMiddleware, invitationController.acceptInvitation);

/**
 * @swagger
 * /api/invitations/accept-public/{token}:
 *   post:
 *     summary: Accept invitation with email verification (no auth required)
 *     tags: [Invitations]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address to verify against invitation
 *             required:
 *               - email
 *     responses:
 *       200:
 *         description: Invitation accepted successfully
 *       400:
 *         description: Invalid token or email mismatch
 *       404:
 *         description: Invalid or expired invitation
 */
router.post("/accept-public/:token", invitationController.acceptInvitationPublic);

/**
 * @swagger
 * /api/invitations/debug/list:
 *   get:
 *     summary: Debug endpoint to list all invitations (development only)
 *     tags: [Invitations]
 *     responses:
 *       200:
 *         description: List of all invitations
 */
router.get("/debug/list", invitationController.debugListInvitations);

export default router;
