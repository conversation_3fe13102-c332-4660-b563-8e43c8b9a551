openapi: 3.0.0
info:
  title: DeepLedger API
  description: API documentation for DeepLedger backend services
  version: 1.0.0
  contact:
    name: DeepLedger Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3001/api
    description: Development server
  - url: https://api.deepledger.com/api
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        full_name:
          type: string
          nullable: true
        avatar_url:
          type: string
          format: uri
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    LoginResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer

    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: object
        message:
          type: string
        error:
          type: string
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

    PaginatedResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            pagination:
              type: object
              properties:
                page:
                  type: integer
                limit:
                  type: integer
                total:
                  type: integer
                totalPages:
                  type: integer

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    Conflict:
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

tags:
  - name: Authentication
    description: Authentication endpoints
  - name: Users
    description: User management endpoints
  - name: Admin
    description: Admin-only endpoints

paths:
  /health:
    get:
      summary: Health check
      tags: [System]
      responses:
        '200':
          description: API is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  timestamp:
                    type: string
                    format: date-time
                  uptime:
                    type: number

security:
  - bearerAuth: []
