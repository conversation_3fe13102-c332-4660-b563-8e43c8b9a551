import { Request, Response } from "express";
import { UserService } from "../services/user.service";
import {
  UpdateUserRequest,
  ApiResponse,
  UserProfile,
  PaginatedResponse,
} from "../models/user.model";
import { asyncHandler } from "../middleware/error.middleware";
import Jo<PERSON> from "joi";

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * Get current user profile
   */
  getProfile = asyncHandler(
    async (req: Request, res: Response<ApiResponse<UserProfile>>) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "User not authenticated",
        });
      }

      const profile = await this.userService.getUserProfile(req.user.id);

      res.json({
        success: true,
        data: profile,
      });
    }
  );

  /**
   * Update user profile
   */
  updateProfile = asyncHandler(
    async (req: Request, res: Response<ApiResponse<UserProfile>>) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "User not authenticated",
        });
      }

      // Validate request body
      const schema = Joi.object({
        full_name: Joi.string().max(100).optional().allow(null),
        avatar_url: Joi.string().uri().optional().allow(null),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const updateData: UpdateUserRequest = value;
      const updatedProfile = await this.userService.updateUserProfile(
        req.user.id,
        updateData
      );

      res.json({
        success: true,
        data: updatedProfile,
        message: "Profile updated successfully",
      });
    }
  );

  /**
   * Get user by ID (admin only)
   */
  getUserById = asyncHandler(
    async (req: Request, res: Response<ApiResponse<UserProfile>>) => {
      // Validate request params
      const schema = Joi.object({
        id: Joi.string().uuid().required(),
      });

      const { error, value } = schema.validate(req.params);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Invalid user ID",
        });
      }

      const profile = await this.userService.getUserProfile(value.id);

      res.json({
        success: true,
        data: profile,
      });
    }
  );

  /**
   * Get all users with pagination (admin only)
   */
  getAllUsers = asyncHandler(
    async (req: Request, res: Response<PaginatedResponse<UserProfile>>) => {
      // Validate query parameters
      const schema = Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(10),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Invalid query parameters",
          errors: this.formatJoiErrors(error),
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
        });
      }

      const result = await this.userService.getAllUsers(
        value.page,
        value.limit
      );

      res.json(result);
    }
  );

  /**
   * Search user by email (admin only)
   */
  searchUserByEmail = asyncHandler(
    async (req: Request, res: Response<ApiResponse<UserProfile | null>>) => {
      // Validate query parameters
      const schema = Joi.object({
        email: Joi.string().email().required(),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Invalid email parameter",
          errors: this.formatJoiErrors(error),
        });
      }

      const user = await this.userService.searchUserByEmail(value.email);

      res.json({
        success: true,
        data: user,
      });
    }
  );

  /**
   * Delete user account
   */
  deleteAccount = asyncHandler(
    async (req: Request, res: Response<ApiResponse>) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "User not authenticated",
        });
      }

      await this.userService.deleteUser(req.user.id);

      res.json({
        success: true,
        message: "Account deleted successfully",
      });
    }
  );

  /**
   * Delete user by ID (admin only)
   */
  deleteUserById = asyncHandler(
    async (req: Request, res: Response<ApiResponse>) => {
      // Validate request params
      const schema = Joi.object({
        id: Joi.string().uuid().required(),
      });

      const { error, value } = schema.validate(req.params);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Invalid user ID",
        });
      }

      await this.userService.deleteUser(value.id);

      res.json({
        success: true,
        message: "User deleted successfully",
      });
    }
  );

  /**
   * Get user statistics (admin only)
   */
  getUserStatistics = asyncHandler(
    async (req: Request, res: Response<ApiResponse>) => {
      const statistics = await this.userService.getUserStatistics();

      res.json({
        success: true,
        data: statistics,
      });
    }
  );

  /**
   * Check if user exists by email
   */
  checkUserExists = asyncHandler(
    async (req: Request, res: Response<ApiResponse<{ exists: boolean }>>) => {
      // Validate query parameters
      const schema = Joi.object({
        email: Joi.string().email().required(),
      });

      const { error, value } = schema.validate(req.query);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Invalid email parameter",
          errors: this.formatJoiErrors(error),
        });
      }

      const exists = await this.userService.userExistsByEmail(value.email);

      res.json({
        success: true,
        data: { exists },
      });
    }
  );

  /**
   * Format Joi validation errors
   */
  private formatJoiErrors(
    error: Joi.ValidationError
  ): Record<string, string[]> {
    const errors: Record<string, string[]> = {};

    error.details.forEach((detail) => {
      const field = detail.path.join(".");
      if (!errors[field]) {
        errors[field] = [];
      }
      errors[field].push(detail.message);
    });

    return errors;
  }
}
