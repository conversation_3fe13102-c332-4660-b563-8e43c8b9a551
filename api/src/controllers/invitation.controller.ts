import { Request, Response } from "express";
import { InvitationService } from "../services/invitation.service";
import {
  InviteUserRequest,
  InvitationResponse,
  SearchUsersRequest,
  ApiResponse,
  AcceptInvitationRequest,
} from "../models/user.model";
import { async<PERSON><PERSON><PERSON> } from "../middleware/error.middleware";
import <PERSON><PERSON> from "joi";

export class InvitationController {
  private invitationService: InvitationService;

  constructor() {
    this.invitationService = new InvitationService();
  }

  /**
   * Search users validation schema
   */
  private searchUsersSchema = Joi.object({
    query: Joi.string().min(2).max(100).required(),
    organization_id: Joi.string().uuid().optional(),
    limit: Joi.number().integer().min(1).max(50).optional().default(10),
  });

  /**
   * Invite user validation schema
   */
  private inviteUserSchema = Joi.object({
    email: Joi.string().email().required(),
    role: Joi.string().valid('admin', 'member', 'viewer').required(),
    message: Joi.string().max(500).optional(),
  });

  /**
   * Invitation response validation schema
   */
  private invitationResponseSchema = Joi.object({
    action: Joi.string().valid('accept', 'decline').required(),
  });

  /**
   * Accept invitation validation schema
   */
  private acceptInvitationSchema = Joi.object({
    token: Joi.string().required(),
    user_data: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().min(8).required(),
      full_name: Joi.string().optional(),
    }).optional(),
  });

  /**
   * Search users for invitation
   */
  searchUsers = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { error, value } = this.searchUsersSchema.validate(req.query);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const searchRequest: SearchUsersRequest = value;
    const users = await this.invitationService.searchUsers(searchRequest);

    res.json({
      success: true,
      data: users,
    });
  });

  /**
   * Invite user to organization
   */
  inviteUser = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    // For testing without auth, use a default user ID
    const userId = req.user?.id || '608dd75c-a66e-4b14-9d68-00cd0aedc790';

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const { error, value } = this.inviteUserSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const inviteData: InviteUserRequest = value;

    // Get the frontend URL from environment or use default
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5174';

    const invitation = await this.invitationService.inviteUser(
      organizationId,
      inviteData,
      userId,
      frontendUrl
    );

    res.status(201).json({
      success: true,
      data: invitation,
      message: "Invitation sent successfully",
    });
  });

  /**
   * Get organization invitations
   */
  getOrganizationInvitations = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const invitations = await this.invitationService.getOrganizationInvitations(
      organizationId,
      userId
    );

    res.json({
      success: true,
      data: invitations,
    });
  });

  /**
   * Get pending invitations for current user
   */
  getPendingInvitations = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const userEmail = req.user!.email;

    const invitations = await this.invitationService.getPendingInvitations(userEmail);

    res.json({
      success: true,
      data: invitations,
    });
  });

  /**
   * Respond to invitation (accept/decline)
   */
  respondToInvitation = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { invitationId } = req.params;
    const userId = req.user!.id;

    if (!invitationId) {
      res.status(400).json({
        success: false,
        error: "Invitation ID is required",
      });
      return;
    }

    const { error, value } = this.invitationResponseSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const response: InvitationResponse = {
      invitation_id: invitationId,
      action: value.action,
    };

    const invitation = await this.invitationService.respondToInvitation(
      invitationId,
      response,
      userId
    );

    res.json({
      success: true,
      data: invitation,
      message: `Invitation ${value.action}ed successfully`,
    });
  });

  /**
   * Cancel invitation
   */
  cancelInvitation = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { invitationId } = req.params;
    const userId = req.user!.id;

    if (!invitationId) {
      res.status(400).json({
        success: false,
        error: "Invitation ID is required",
      });
      return;
    }

    await this.invitationService.cancelInvitation(invitationId, userId);

    res.json({
      success: true,
      message: "Invitation canceled successfully",
    });
  });

  /**
   * Get invitation statistics
   */
  getInvitationStats = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const stats = await this.invitationService.getInvitationStats(organizationId, userId);

    res.json({
      success: true,
      data: stats,
    });
  });

  /**
   * Verify invitation token
   */
  verifyInvitation = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { token } = req.params;

    console.log('🔍 Verifying invitation token:', token);

    if (!token) {
      res.status(400).json({
        success: false,
        error: "Invitation token is required",
      });
      return;
    }

    try {
      const verification = await this.invitationService.verifyInvitation(token);

      if (!verification) {
        console.log('❌ Invitation verification failed: Token not found or expired');
        res.status(404).json({
          success: false,
          error: "Invalid or expired invitation",
        });
        return;
      }

      console.log('✅ Invitation verified successfully for:', verification.invitation.email);
      res.json({
        success: true,
        data: verification,
      });
    } catch (error) {
      console.error('❌ Error during invitation verification:', error);
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : "Invalid or expired invitation",
      });
    }
  });

  /**
   * Accept invitation (for existing users)
   */
  acceptInvitation = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { token } = req.params;
    const userId = req.user!.id;

    if (!token) {
      res.status(400).json({
        success: false,
        error: "Invitation token is required",
      });
      return;
    }

    const invitation = await this.invitationService.acceptInvitation(token, userId);

    res.json({
      success: true,
      data: invitation,
      message: "Invitation accepted successfully",
    });
  });

  /**
   * Accept invitation with email verification (no auth required)
   */
  acceptInvitationPublic = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { token } = req.params;
    const { email } = req.body;

    console.log('🔍 Controller: acceptInvitationPublic called');
    console.log('🔍 Controller: Token:', token);
    console.log('🔍 Controller: Request body:', req.body);
    console.log('🔍 Controller: Email from body:', email);

    if (!token) {
      console.log('❌ Controller: Token is missing');
      res.status(400).json({
        success: false,
        error: "Invitation token is required",
      });
      return;
    }

    if (!email) {
      console.log('❌ Controller: Email is missing from request body');
      console.log('❌ Controller: Full request body:', JSON.stringify(req.body));
      res.status(400).json({
        success: false,
        error: "Email is required",
      });
      return;
    }

    try {
      const result = await this.invitationService.acceptInvitationPublic(token, email);

      res.json({
        success: true,
        data: result,
        message: "Invitation accepted successfully",
      });
    } catch (error) {
      console.error('Error in acceptInvitationPublic:', error);
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to accept invitation",
      });
    }
  });

  /**
   * Debug endpoint to list all invitations (for development)
   */
  debugListInvitations = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    try {
      const invitations = await this.invitationService.debugListAllInvitations();

      res.json({
        success: true,
        data: {
          total: invitations.length,
          invitations: invitations.map(inv => ({
            invitation_id: inv.invitation_id,
            email: inv.email,
            status: inv.status,
            verification_token: inv.verification_token,
            expires_at: inv.expires_at,
            invited_at: inv.invited_at
          }))
        },
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Failed to list invitations",
      });
    }
  });
}
