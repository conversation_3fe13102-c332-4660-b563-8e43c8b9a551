import { Request, Response } from "express";
import { RoleService } from "../services/role.service";
import { CreateRoleRequest, UpdateRoleRequest } from "../models/role.model";
import Joi from "joi";

export class RoleController {
  private roleService: RoleService;

  constructor() {
    this.roleService = new RoleService();
  }

  /**
   * Create role validation schema
   */
  private createRoleSchema = Joi.object({
    name: Joi.string().min(2).max(50).required(),
    display_name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    permissions: Joi.array().items(Joi.string()).optional(),
  });

  /**
   * Update role validation schema
   */
  private updateRoleSchema = Joi.object({
    display_name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    permissions: Joi.array().items(Joi.string()).optional(),
  });

  /**
   * Get all roles
   */
  getAllRoles = async (req: Request, res: Response): Promise<void> => {
    try {
      const roles = await this.roleService.getAllRoles();

      res.status(200).json({
        success: true,
        data: roles,
        message: "Roles retrieved successfully",
      });
    } catch (error) {
      console.error("Error in getAllRoles:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  };

  /**
   * Get system roles only
   */
  getSystemRoles = async (req: Request, res: Response): Promise<void> => {
    try {
      const roles = await this.roleService.getSystemRoles();

      res.status(200).json({
        success: true,
        data: roles,
        message: "System roles retrieved successfully",
      });
    } catch (error) {
      console.error("Error in getSystemRoles:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  };

  /**
   * Get role by ID
   */
  getRoleById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { roleId } = req.params;

      if (!roleId) {
        res.status(400).json({
          success: false,
          error: "Role ID is required",
        });
        return;
      }

      const role = await this.roleService.getRoleById(roleId);

      if (!role) {
        res.status(404).json({
          success: false,
          error: "Role not found",
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: role,
        message: "Role retrieved successfully",
      });
    } catch (error) {
      console.error("Error in getRoleById:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  };

  /**
   * Get assignable roles for current user
   */
  getAssignableRoles = async (req: Request, res: Response): Promise<void> => {
    try {
      const userRole = req.query.userRole as string;

      if (!userRole) {
        res.status(400).json({
          success: false,
          error: "User role is required",
        });
        return;
      }

      const roles = await this.roleService.getAssignableRoles(userRole);

      res.status(200).json({
        success: true,
        data: roles,
        message: "Assignable roles retrieved successfully",
      });
    } catch (error) {
      console.error("Error in getAssignableRoles:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  };

  /**
   * Create a custom role
   */
  createRole = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.createRoleSchema.validate(req.body);

      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      const roleData: CreateRoleRequest = value;
      const role = await this.roleService.createRole(roleData);

      res.status(201).json({
        success: true,
        data: role,
        message: "Role created successfully",
      });
    } catch (error) {
      console.error("Error in createRole:", error);
      if (error instanceof Error && error.message === "Role name already exists") {
        res.status(409).json({
          success: false,
          error: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  };

  /**
   * Update a custom role
   */
  updateRole = async (req: Request, res: Response): Promise<void> => {
    try {
      const { roleId } = req.params;
      const { error, value } = this.updateRoleSchema.validate(req.body);

      if (error) {
        res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
        return;
      }

      if (!roleId) {
        res.status(400).json({
          success: false,
          error: "Role ID is required",
        });
        return;
      }

      const updateData: UpdateRoleRequest = value;
      const role = await this.roleService.updateRole(roleId, updateData);

      res.status(200).json({
        success: true,
        data: role,
        message: "Role updated successfully",
      });
    } catch (error) {
      console.error("Error in updateRole:", error);
      if (error instanceof Error) {
        if (error.message === "Role not found") {
          res.status(404).json({
            success: false,
            error: error.message,
          });
        } else if (error.message === "Cannot update system roles") {
          res.status(403).json({
            success: false,
            error: error.message,
          });
        } else {
          res.status(500).json({
            success: false,
            error: "Internal server error",
          });
        }
      } else {
        res.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  };

  /**
   * Delete a custom role
   */
  deleteRole = async (req: Request, res: Response): Promise<void> => {
    try {
      const { roleId } = req.params;

      if (!roleId) {
        res.status(400).json({
          success: false,
          error: "Role ID is required",
        });
        return;
      }

      await this.roleService.deleteRole(roleId);

      res.status(200).json({
        success: true,
        message: "Role deleted successfully",
      });
    } catch (error) {
      console.error("Error in deleteRole:", error);
      if (error instanceof Error) {
        if (error.message === "Role not found") {
          res.status(404).json({
            success: false,
            error: error.message,
          });
        } else if (error.message === "Cannot delete system roles") {
          res.status(403).json({
            success: false,
            error: error.message,
          });
        } else {
          res.status(500).json({
            success: false,
            error: "Internal server error",
          });
        }
      } else {
        res.status(500).json({
          success: false,
          error: "Internal server error",
        });
      }
    }
  };
}
