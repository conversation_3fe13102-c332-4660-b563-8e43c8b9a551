import { Request, Response } from "express";
import { OrganizationService } from "../services/organization.service";
import {
  CreateOrganizationRequest,
  UpdateOrganizationRequest,
  AddOrganizationUserRequest,
  UpdateOrganizationUserRequest,
  ApiResponse,
} from "../models/user.model";
import { asyncHand<PERSON> } from "../middleware/error.middleware";
import Jo<PERSON> from "joi";

export class OrganizationController {
  private organizationService: OrganizationService;

  constructor() {
    this.organizationService = new OrganizationService();
  }

  /**
   * Create organization validation schema
   */
  private createOrganizationSchema = Joi.object({
    name: Joi.string().min(1).max(100).required(),
    slug: Joi.string().min(1).max(50).pattern(/^[a-z0-9-]+$/).optional(),
    logo: Joi.string().uri().optional(),
    settings: Joi.object().optional(),
  });

  /**
   * Update organization validation schema
   */
  private updateOrganizationSchema = Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    slug: Joi.string().min(1).max(50).pattern(/^[a-z0-9-]+$/).optional(),
    logo: Joi.string().uri().optional(),
    settings: Joi.object().optional(),
  });

  /**
   * Add user validation schema
   */
  private addUserSchema = Joi.object({
    user_id: Joi.string().uuid().required(),
    role: Joi.string().valid('admin', 'accountant', 'member', 'viewer').required(),
  });

  /**
   * Update user role validation schema
   */
  private updateUserRoleSchema = Joi.object({
    role: Joi.string().valid('admin', 'accountant', 'member', 'viewer').required(),
  });

  /**
   * Create a new organization
   */
  createOrganization = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { error, value } = this.createOrganizationSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const organizationData: CreateOrganizationRequest = value;
    const userId = req.user!.id;

    const organization = await this.organizationService.createOrganization(
      organizationData,
      userId
    );

    res.status(201).json({
      success: true,
      data: organization,
      message: "Organization created successfully",
    });
  });

  /**
   * Get organization by ID
   */
  getOrganization = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const organization = await this.organizationService.getOrganization(organizationId, userId);

    res.json({
      success: true,
      data: organization,
    });
  });

  /**
   * Update organization
   */
  updateOrganization = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const { error, value } = this.updateOrganizationSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const updateData: UpdateOrganizationRequest = value;

    const organization = await this.organizationService.updateOrganization(
      organizationId,
      updateData,
      userId
    );

    res.json({
      success: true,
      data: organization,
      message: "Organization updated successfully",
    });
  });

  /**
   * Delete organization
   */
  deleteOrganization = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    await this.organizationService.deleteOrganization(organizationId, userId);

    res.json({
      success: true,
      message: "Organization deleted successfully",
    });
  });

  /**
   * Get user's organizations
   */
  getUserOrganizations = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const userId = req.user!.id;

    const organizations = await this.organizationService.getUserOrganizations(userId);

    res.json({
      success: true,
      data: organizations,
    });
  });

  /**
   * Add user to organization
   */
  addUser = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const requestingUserId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const { error, value } = this.addUserSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const userData: AddOrganizationUserRequest = value;

    const organizationUser = await this.organizationService.addUserToOrganization(
      organizationId,
      userData,
      requestingUserId
    );

    res.status(201).json({
      success: true,
      data: organizationUser,
      message: "User added to organization successfully",
    });
  });

  /**
   * Update user role in organization
   */
  updateUserRole = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId, userId } = req.params;
    const requestingUserId = req.user!.id;

    if (!organizationId || !userId) {
      res.status(400).json({
        success: false,
        error: "Organization ID and User ID are required",
      });
      return;
    }

    const { error, value } = this.updateUserRoleSchema.validate(req.body);
    if (error) {
      res.status(400).json({
        success: false,
        error: "Validation error",
        errors: { validation: [error.details[0].message] },
      });
      return;
    }

    const updateData: UpdateOrganizationUserRequest = value;

    const organizationUser = await this.organizationService.updateUserRole(
      organizationId,
      userId,
      updateData,
      requestingUserId
    );

    res.json({
      success: true,
      data: organizationUser,
      message: "User role updated successfully",
    });
  });

  /**
   * Remove user from organization
   */
  removeUser = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId, userId } = req.params;
    const requestingUserId = req.user!.id;

    if (!organizationId || !userId) {
      res.status(400).json({
        success: false,
        error: "Organization ID and User ID are required",
      });
      return;
    }

    await this.organizationService.removeUserFromOrganization(
      organizationId,
      userId,
      requestingUserId
    );

    res.json({
      success: true,
      message: "User removed from organization successfully",
    });
  });

  /**
   * Leave organization
   */
  leaveOrganization = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const userId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    await this.organizationService.leaveOrganization(organizationId, userId);

    res.json({
      success: true,
      message: "Left organization successfully",
    });
  });

  /**
   * Get organization members
   */
  getMembers = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const requestingUserId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const members = await this.organizationService.getOrganizationMembers(
      organizationId,
      requestingUserId
    );

    res.json({
      success: true,
      data: members,
    });
  });

  /**
   * Get organization member statistics
   */
  getMemberStats = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    const { organizationId } = req.params;
    const requestingUserId = req.user!.id;

    if (!organizationId) {
      res.status(400).json({
        success: false,
        error: "Organization ID is required",
      });
      return;
    }

    const stats = await this.organizationService.getOrganizationMemberStats(
      organizationId,
      requestingUserId
    );

    res.json({
      success: true,
      data: stats,
    });
  });
}
