import { Request, Response } from "express";
import { AuthService } from "../services/auth.service";
import {
  CreateUserRequest,
  LoginRequest,
  RefreshTokenRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  ApiResponse,
  LoginResponse,
  RefreshTokenResponse,
  UserProfile,
} from "../models/user.model";
import { asyncHandler } from "../middleware/error.middleware";
import Joi from "joi";

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * Register a new user
   */
  register = asyncHandler(
    async (req: Request, res: Response<ApiResponse<LoginResponse>>) => {
      // Validate request body
      const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(8).required(),
        full_name: Joi.string().max(100).optional(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const userData: CreateUserRequest = value;
      const result = await this.authService.register(userData);

      res.status(201).json({
        success: true,
        data: result,
        message: "User registered successfully",
      });
    }
  );

  /**
   * Login user
   */
  login = asyncHandler(
    async (req: Request, res: Response<ApiResponse<LoginResponse>>) => {
      // Validate request body
      const schema = Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const credentials: LoginRequest = value;
      const result = await this.authService.login(credentials);

      res.json({
        success: true,
        data: result,
        message: "Login successful",
      });
    }
  );

  /**
   * Refresh access token
   */
  refreshToken = asyncHandler(
    async (req: Request, res: Response<ApiResponse<RefreshTokenResponse>>) => {
      // Validate request body
      const schema = Joi.object({
        refresh_token: Joi.string().required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const request: RefreshTokenRequest = value;
      const result = await this.authService.refreshToken(request);

      res.json({
        success: true,
        data: result,
        message: "Token refreshed successfully",
      });
    }
  );

  /**
   * Logout user
   */
  logout = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
    await this.authService.logout();

    res.json({
      success: true,
      message: "Logout successful",
    });
  });

  /**
   * Get current user profile
   */
  getProfile = asyncHandler(
    async (req: Request, res: Response<ApiResponse<UserProfile>>) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "User not authenticated",
        });
      }

      const profile = await this.authService.getProfile(req.user.id);

      res.json({
        success: true,
        data: profile,
      });
    }
  );

  /**
   * Reset password
   */
  resetPassword = asyncHandler(
    async (req: Request, res: Response<ApiResponse>) => {
      // Validate request body
      const schema = Joi.object({
        email: Joi.string().email().required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const request: ResetPasswordRequest = value;
      await this.authService.resetPassword(request);

      res.json({
        success: true,
        message: "Password reset email sent",
      });
    }
  );

  /**
   * Change password
   */
  changePassword = asyncHandler(
    async (req: Request, res: Response<ApiResponse>) => {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: "User not authenticated",
        });
      }

      // Validate request body
      const schema = Joi.object({
        current_password: Joi.string().required(),
        new_password: Joi.string().min(8).required(),
      });

      const { error, value } = schema.validate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          errors: this.formatJoiErrors(error),
        });
      }

      const request: ChangePasswordRequest = value;
      await this.authService.changePassword(req.user.id, request);

      res.json({
        success: true,
        message: "Password changed successfully",
      });
    }
  );

  /**
   * Format Joi validation errors
   */
  private formatJoiErrors(
    error: Joi.ValidationError
  ): Record<string, string[]> {
    const errors: Record<string, string[]> = {};

    error.details.forEach((detail) => {
      const field = detail.path.join(".");
      if (!errors[field]) {
        errors[field] = [];
      }
      errors[field].push(detail.message);
    });

    return errors;
  }
}
