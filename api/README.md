# DeepLedger API

A robust Node.js/TypeScript API backend for the DeepLedger application, built with Express.js and Supabase.

## Features

- 🔐 **Authentication & Authorization**: JWT-based auth with Supabase integration
- 👥 **User Management**: Complete user CRUD operations
- 🛡️ **Security**: Helmet, CORS, rate limiting, input validation
- 📝 **Logging**: Structured logging with Winston
- 🧪 **Testing**: Jest test suite with supertest
- 📚 **Documentation**: Swagger/OpenAPI documentation
- 🏗️ **Architecture**: Clean architecture with repositories, services, and controllers
- 🔧 **TypeScript**: Full TypeScript support with strict configuration

## Project Structure

```
api/
├── src/
│   ├── config/          # Configuration files
│   ├── controllers/     # Request handlers
│   ├── middleware/      # Express middleware
│   ├── models/          # Data models/interfaces
│   ├── repositories/    # Data access layer
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   ├── utils/           # Helper functions
│   ├── docs/            # API documentation
│   └── app.ts           # Express app setup
├── tests/               # Test files
├── .env.*               # Environment variables
├── package.json         # Dependencies
└── tsconfig.json        # TypeScript configuration
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account and project

### Installation

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Environment Setup**:
   Copy the appropriate environment file and update with your values:

   ```bash
   cp .env.development .env
   ```

   Update the following variables in your `.env` file:

   ```env
   SUPABASE_URL=your_supabase_url_here
   SUPABASE_ANON_KEY=your_supabase_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
   JWT_SECRET=your_jwt_secret_here
   DATABASE_URL=your_database_url_here
   ```

3. **Build the project**:
   ```bash
   npm run build
   ```

### Development

Start the development server with hot reload:

```bash
npm run dev
```

The API will be available at `http://localhost:3001`

### Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Production

Build and start the production server:

```bash
npm run build
npm start
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/profile` - Get current user profile
- `POST /api/auth/reset-password` - Reset password
- `POST /api/auth/change-password` - Change password

### Users

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `DELETE /api/users/account` - Delete user account
- `GET /api/users/exists` - Check if user exists by email

### Admin (requires admin role)

- `GET /api/users` - Get all users with pagination
- `GET /api/users/search` - Search user by email
- `GET /api/users/statistics` - Get user statistics
- `GET /api/users/:id` - Get user by ID
- `DELETE /api/users/:id` - Delete user by ID

### System

- `GET /health` - Health check
- `GET /api/docs` - API documentation (development only)

## Environment Variables

| Variable                    | Description                               | Required                            |
| --------------------------- | ----------------------------------------- | ----------------------------------- |
| `NODE_ENV`                  | Environment (development/production/test) | No                                  |
| `PORT`                      | Server port                               | No (default: 3001)                  |
| `SUPABASE_URL`              | Supabase project URL                      | Yes                                 |
| `SUPABASE_ANON_KEY`         | Supabase anonymous key                    | Yes                                 |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key                 | Yes                                 |
| `JWT_SECRET`                | JWT signing secret                        | Yes                                 |
| `JWT_EXPIRES_IN`            | JWT expiration time                       | No (default: 24h)                   |
| `JWT_REFRESH_EXPIRES_IN`    | Refresh token expiration                  | No (default: 7d)                    |
| `DATABASE_URL`              | Database connection URL                   | Yes                                 |
| `CORS_ORIGIN`               | CORS allowed origin                       | No (default: http://localhost:5174) |
| `LOG_LEVEL`                 | Logging level                             | No (default: info)                  |

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.
