# Supabase Configuration Setup

This document outlines the Supabase configuration that has been applied to the DeepLedger API and what additional steps are needed.

## ✅ Completed Configuration

### Supabase Project Details

- **Project Name**: deepledger
- **Project ID**: iajycvybkkrwmhkompec
- **Region**: us-west-1
- **Status**: ACTIVE_HEALTHY
- **URL**: https://iajycvybkkrwmhkompec.supabase.co

### API Keys (Already Updated in Environment Files)

- **Anonymous Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.hexGPbcg2iMheWmQYpgLR-QWmXawPHgkdZ3gfQIrTGQ`
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.Is19UEdTE6Apkxv7xgSm0o-GIKxA1ozXSG-tJrZofKk`

### Environment Files Updated

- ✅ `api/.env.development` - Development configuration
- ✅ `api/.env.production` - Production configuration
- ✅ `api/.env.test` - Test configuration

## 🔧 Additional Setup Required

### 1. Database Password

The database URL in all environment files contains `[YOUR-PASSWORD]` placeholder. You need to:

1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/iajycvybkkrwmhkompec
2. Navigate to Settings > Database
3. Find your database password or reset it
4. Replace `[YOUR-PASSWORD]` in the DATABASE_URL in all environment files

**Current DATABASE_URL format:**

```
postgresql://postgres:[YOUR-PASSWORD]@db.iajycvybkkrwmhkompec.supabase.co:5432/postgres
```

### 2. JWT Secrets

- **Development**: Uses a placeholder secret (should be changed for security)
- **Production**: **MUST** be changed to a secure random string
- **Test**: Uses a test-specific secret (acceptable for testing)

**To generate a secure JWT secret:**

```bash
# Option 1: Using Node.js
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

# Option 2: Using OpenSSL
openssl rand -hex 64
```

### 3. Production CORS Origin

Update the `CORS_ORIGIN` in `.env.production` with your actual production domain.

## 🚀 Getting Started

1. **Install dependencies:**

   ```bash
   cd api
   npm install
   ```

2. **Update database password:**

   - Get your database password from Supabase dashboard
   - Replace `[YOUR-PASSWORD]` in all `.env.*` files

3. **Update JWT secrets:**

   - Generate secure random strings for development and production
   - Update the `JWT_SECRET` values in the environment files

4. **Start development server:**

   ```bash
   npm run dev
   ```

5. **Test the API:**

   ```bash
   # Health check
   curl http://localhost:3001/health

   # API documentation
   curl http://localhost:3001/api/docs
   ```

## 📋 Database Schema

The API expects the following database schema in Supabase:

### Users Table

```sql
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);
```

## 🔐 Security Notes

1. **Service Role Key**: Keep this secret and never expose it in client-side code
2. **JWT Secret**: Use strong, random secrets in production
3. **Database Password**: Store securely and rotate regularly
4. **Environment Files**: Never commit actual `.env` files to version control

## 🔗 Frontend Integration

The frontend is already configured to use the same Supabase project:

- **Frontend URL**: https://iajycvybkkrwmhkompec.supabase.co
- **Frontend Anon Key**: Same as API anon key
- **Frontend runs on**: http://localhost:5174

Both frontend and API will share the same Supabase authentication system.

## 📚 Next Steps

1. Set up the database schema in Supabase
2. Configure Row Level Security (RLS) policies
3. Test authentication flow between frontend and API
4. Set up email templates in Supabase for auth emails
5. Configure production deployment settings
