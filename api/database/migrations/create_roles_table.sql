-- Create Roles table with predefined organization roles
CREATE TABLE roles (
  role_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  display_name TEXT NOT NULL,
  description TEXT,
  permissions JSONB DEFAULT '[]'::jsonb,
  is_system_role BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all authenticated users to read roles
CREATE POLICY "Authenticated users can view roles" ON roles
  FOR SELECT USING (auth.role() = 'authenticated');

-- Insert predefined roles
INSERT INTO roles (name, display_name, description, permissions) VALUES
  ('owner', 'Owner', 'Full access to organization including deletion and ownership transfer', '["read", "write", "admin", "owner"]'::jsonb),
  ('admin', 'Admin', 'Can manage organization settings and invite/remove users', '["read", "write", "admin"]'::jsonb),
  ('accountant', 'Accountant', 'Can view and manage financial data and reports', '["read", "write", "financial"]'::jsonb),
  ('member', 'Member', 'Can view and edit organization data', '["read", "write"]'::jsonb),
  ('viewer', 'Viewer', 'Can only view organization data', '["read"]'::jsonb);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS roles_name_idx ON roles(name);
CREATE INDEX IF NOT EXISTS roles_is_system_role_idx ON roles(is_system_role);
