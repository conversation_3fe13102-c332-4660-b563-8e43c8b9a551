-- Create function to search users with emails without requiring admin privileges
-- This function joins public.users with auth.users to get email information

CREATE OR REPLACE FUNCTION search_users_with_emails(
  search_query TEXT,
  result_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  phone TEXT,
  timezone TEXT,
  preferences JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.user_id,
    au.email::TEXT,
    u.first_name,
    u.last_name,
    u.avatar_url,
    u.phone,
    u.timezone,
    u.preferences,
    u.created_at,
    u.updated_at
  FROM public.users u
  INNER JOIN auth.users au ON u.user_id = au.id
  WHERE 
    LOWER(au.email) = LOWER(search_query)
    OR LOWER(au.email) LIKE LOWER('%' || search_query || '%')
    OR LOWER(COALESCE(u.first_name, '')) LIKE LOWER('%' || search_query || '%')
    OR LOWER(COALESCE(u.last_name, '')) LIKE LOWER('%' || search_query || '%')
    OR LOWER(COALESCE(u.first_name || ' ' || u.last_name, '')) LIKE LOWER('%' || search_query || '%')
  ORDER BY 
    -- Exact email match first
    CASE WHEN LOWER(au.email) = LOWER(search_query) THEN 1 ELSE 2 END,
    -- Then email starts with query
    CASE WHEN LOWER(au.email) LIKE LOWER(search_query || '%') THEN 1 ELSE 2 END,
    -- Then by name
    COALESCE(u.first_name, ''), COALESCE(u.last_name, '')
  LIMIT result_limit;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION search_users_with_emails(TEXT, INTEGER) TO authenticated;

-- Create an index on auth.users.email for better performance (if it doesn't exist)
-- Note: This might already exist, so we use IF NOT EXISTS
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'auth' 
    AND tablename = 'users' 
    AND indexname = 'users_email_idx'
  ) THEN
    CREATE INDEX users_email_idx ON auth.users(email);
  END IF;
END $$;

-- Create indexes on public.users for better search performance
CREATE INDEX IF NOT EXISTS users_first_name_idx ON public.users(first_name);
CREATE INDEX IF NOT EXISTS users_last_name_idx ON public.users(last_name);
CREATE INDEX IF NOT EXISTS users_full_name_idx ON public.users((first_name || ' ' || last_name));
