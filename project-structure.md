api/
├── src/
│ ├── config/ # Configuration files
│ │ ├── supabase.ts # Supabase client setup
│ │ └── env.ts # Environment variables
│ ├── controllers/ # Request handlers
│ │ ├── auth.controller.ts # Authentication endpoints
│ │ └── user.controller.ts # User management endpoints
│ ├── middleware/ # Express middleware
│ │ ├── auth.middleware.ts # JWT validation
│ │ ├── error.middleware.ts # Error handling
│ │ └── logger.middleware.ts # Request logging
│ ├── models/ # Data models/interfaces
│ │ └── user.model.ts # User data structure
│ ├── repositories/ # Data access layer
│ │ ├── base.repository.ts # Base repository with common methods
│ │ ├── user.repository.ts # User data operations
│ │ └── auth.repository.ts # Auth data operations
│ ├── routes/ # API routes
│ │ ├── auth.routes.ts # Auth endpoints
│ │ └── user.routes.ts # User endpoints
│ ├── services/ # Business logic
│ │ ├── auth.service.ts # Auth operations
│ │ └── user.service.ts # User operations
│ ├── utils/ # Helper functions
│ │ └── jwt.utils.ts # JWT handling utilities
│ ├── docs/ # Swagger documentation
│ │ └── swagger.yaml # API specs
│ └── app.ts # Express app setup
├── .env.development # Development environment variables
├── .env.production # Production environment variables
├── .env.test # Test environment variables
├── tests/ # Unit and integration tests
├── package.json # Dependencies
└── tsconfig.json # TypeScript configuration
