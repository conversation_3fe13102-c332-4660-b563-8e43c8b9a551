/**
 * Chat and messaging types for the DeepLedger application
 */

// Chat message types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

// Tool call display types
export interface ToolCallDisplay {
  id: string;
  name: string;
  args: Record<string, any>;
  status: 'running' | 'completed' | 'error';
  result?: any;
  error?: string;
  timestamp: Date;
}

// Chat thread types
export interface ChatThread {
  id: string;
  title: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

// Message options for sending
export interface SendMessageOptions {
  threadId?: string;
  resourceId?: string;
  context?: any[];
}

// Response from Mastra backend
export interface MastraResponse {
  message?: string;
  toolCalls?: ToolCallDisplay[];
  error?: string;
  isComplete?: boolean;
}
