// Financial metrics types for the sidebar
export interface TimePeriod {
  id: string;
  label: string;
  startDate: Date;
  endDate: Date;
}

export interface FinancialMetrics {
  netIncome: number;
  totalRevenue: number;
  totalCosts: number;
  totalAssets: number;
  totalLiabilities: number;
  ownersEquity: number;
  period: TimePeriod;
  lastUpdated: Date;
}

export interface FinancialMetricItem {
  id: string;
  label: string;
  value: number;
  change?: number;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: string;
  description: string;
}

export interface FinancialMetricsState {
  metrics: FinancialMetrics | null;
  selectedPeriod: TimePeriod;
  isLoading: boolean;
  error: string | null;
  isCollapsed: boolean;
}

// Predefined time periods
export const TIME_PERIODS: TimePeriod[] = [
  {
    id: 'this-month',
    label: 'This Month',
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  },
  {
    id: 'last-month',
    label: 'Last Month',
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
    endDate: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
  },
  {
    id: 'this-quarter',
    label: 'This Quarter',
    startDate: new Date(new Date().getFullYear(), Math.floor(new Date().getMonth() / 3) * 3, 1),
    endDate: new Date(new Date().getFullYear(), Math.floor(new Date().getMonth() / 3) * 3 + 3, 0),
  },
  {
    id: 'last-quarter',
    label: 'Last Quarter',
    startDate: new Date(new Date().getFullYear(), Math.floor(new Date().getMonth() / 3) * 3 - 3, 1),
    endDate: new Date(new Date().getFullYear(), Math.floor(new Date().getMonth() / 3) * 3, 0),
  },
  {
    id: 'this-year',
    label: 'This Year',
    startDate: new Date(new Date().getFullYear(), 0, 1),
    endDate: new Date(new Date().getFullYear(), 11, 31),
  },
  {
    id: 'last-year',
    label: 'Last Year',
    startDate: new Date(new Date().getFullYear() - 1, 0, 1),
    endDate: new Date(new Date().getFullYear() - 1, 11, 31),
  },
];
