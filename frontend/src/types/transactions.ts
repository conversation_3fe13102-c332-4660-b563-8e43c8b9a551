// Transaction types based on the backend schema
export interface Transaction {
  id: string;
  date: Date;
  description: string;
  reference?: string;
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  status: 'pending' | 'completed' | 'cancelled';
  entries: TransactionEntry[];
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TransactionEntry {
  id: string;
  transactionId: string;
  accountId: string;
  accountName: string;
  debit: number;
  credit: number;
  description?: string;
}

export interface Account {
  id: string;
  name: string;
  code: string;
  type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  parentId?: string;
  balance: number;
  isActive: boolean;
  createdAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive: boolean;
}

export interface Vendor {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  isActive: boolean;
}

export interface Item {
  id: string;
  name: string;
  description?: string;
  price: number;
  categoryId?: string;
  isActive: boolean;
}

// Financial report types
export interface BalanceSheetData {
  assets: AccountBalance[];
  liabilities: AccountBalance[];
  equity: AccountBalance[];
  totalAssets: number;
  totalLiabilities: number;
  totalEquity: number;
  asOfDate: Date;
}

export interface ProfitLossData {
  income: AccountBalance[];
  expenses: AccountBalance[];
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  fromDate: Date;
  toDate: Date;
}

export interface AccountBalance {
  accountId: string;
  accountName: string;
  accountCode: string;
  balance: number;
  children?: AccountBalance[];
}

export interface TrialBalanceData {
  accounts: AccountBalance[];
  totalDebits: number;
  totalCredits: number;
  asOfDate: Date;
}

// Form types for creating transactions
export interface TransactionFormData {
  date: string;
  description: string;
  reference?: string;
  entries: TransactionEntryFormData[];
}

export interface TransactionEntryFormData {
  accountId: string;
  debit: string;
  credit: string;
  description?: string;
}

// Filter and search types
export interface TransactionFilters {
  dateFrom?: Date;
  dateTo?: Date;
  accountId?: string;
  type?: Transaction['type'];
  status?: Transaction['status'];
  searchTerm?: string;
}

export interface ReportFilters {
  fromDate?: Date;
  toDate?: Date;
  accountIds?: string[];
  includeInactive?: boolean;
}
