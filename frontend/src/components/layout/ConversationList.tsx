import React, { useState, useEffect } from 'react';
import { Plus, MessageSquare, Calendar } from 'lucide-react';
import { Button } from '../ui/Button';
import { ChatInterface } from '../chat/ChatInterface';
import { useChatStore } from '../../stores/chat-store';
import { useAuthStore } from '../../stores/auth-store';
import { cn } from '../../utils/cn';

interface ConversationItem {
  id: string;
  title: string;
  preview: string;
  date: string;
  isActive?: boolean;
}

// Mock conversation data - in real app this would come from the chat store
const mockConversations: ConversationItem[] = [
  {
    id: '1',
    title: 'Invoice for 20 Pens Sold to Harvey on 30 Jul...',
    preview: 'Created invoice for Harvey - 20 pens at $2.50 each',
    date: 'June 7',
  },
  {
    id: '2',
    title: 'Create Customer Harvey',
    preview: 'Added new customer Harvey to the system',
    date: 'June 7',
  },
  {
    id: '3',
    title: 'Invoice for 20 Pens Sold to <PERSON><PERSON> at 3 Per Pen',
    preview: 'Generated invoice for <PERSON>agan - bulk pen order',
    date: 'June 7',
  },
  {
    id: '4',
    title: 'New Thread 2025-06-08T02:49:29.129Z',
    preview: 'Started new conversation thread',
    date: 'June 7',
  },
  {
    id: '5',
    title: 'Sales Invoice for 50 Pens to Rani with 30-Da...',
    preview: 'Created sales invoice with payment terms',
    date: 'June 7',
  },
  {
    id: '6',
    title: 'Pen Sale Invoice for Raju - 50 Pens at 4 Each',
    preview: 'Bulk sale invoice for Raju',
    date: 'June 7',
  },
  {
    id: '7',
    title: 'Invoice for 10 Pens Sale to Gopi on 5th June...',
    preview: 'Small order invoice for Gopi',
    date: 'June 7',
  },
  {
    id: '8',
    title: 'Invoice for 10 Pens Sale to Gopi on 4th June...',
    preview: 'Follow-up order from Gopi',
    date: 'June 7',
  },
  {
    id: '9',
    title: 'Sales Invoice for Gopi - 12 Pens at 4 Each',
    preview: 'Another order from Gopi',
    date: 'June 7',
  },
  {
    id: '10',
    title: 'New Thread 2025-06-07T21:30:55.149Z',
    preview: 'System generated thread',
    date: 'June 7',
  },
  {
    id: '11',
    title: 'New Thread 2025-06-07T21:07:20.984Z',
    preview: 'System generated thread',
    date: 'June 7',
  },
];

export function ConversationList() {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const {
    threads,
    currentThreadId,
    createNewThread,
    switchToThread,
    loadThreads,
    isLoadingThreads
  } = useChatStore();
  const { user } = useAuthStore();

  // Load threads when component mounts
  useEffect(() => {
    if (user?.id) {
      loadThreads(user.id).catch((error) => {
        console.error('Failed to load threads:', error);
      });
    }
  }, [user?.id, loadThreads]);

  const handleNewChat = async () => {
    if (user?.id) {
      try {
        await createNewThread(user.id);
        setSelectedConversation(null); // Show the new chat interface
      } catch (error) {
        console.error('Failed to create new thread:', error);
      }
    }
  };

  const handleSelectConversation = (conversationId: string) => {
    setSelectedConversation(conversationId);
    // Also switch to the thread in the chat store
    switchToThread(conversationId);
  };

  // If a conversation is selected, show the full chat interface
  if (selectedConversation || currentThreadId) {
    return <ChatInterface />;
  }

  return (
    <div className="h-full bg-background">
      {/* Header with New Chat button */}
      <div className="p-4 border-b border-border">
        <Button
          onClick={handleNewChat}
          className="w-full justify-start space-x-2 bg-primary hover:bg-primary/90"
        >
          <Plus className="w-4 h-4" />
          <span>New Chat</span>
        </Button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto scrollbar-chat">
        {/* Show loading state */}
        {isLoadingThreads && (
          <div className="p-4 text-center text-muted-foreground">
            Loading conversations...
          </div>
        )}

        {/* Show real threads if available, otherwise show mock data */}
        {(threads.length > 0 ? threads : mockConversations).map((conversation) => {
          // Handle both thread objects and mock conversation objects
          const id = 'id' in conversation ? conversation.id : conversation.id;
          const title = 'title' in conversation ? conversation.title : conversation.title;
          const preview = 'title' in conversation ? `Thread: ${conversation.title}` : conversation.preview;
          const date = 'updatedAt' in conversation
            ? new Date(conversation.updatedAt).toLocaleDateString()
            : conversation.date;

          return (
            <div
              key={id}
              onClick={() => handleSelectConversation(id)}
              className={cn(
                'p-4 border-b border-border/50 cursor-pointer transition-colors hover:bg-accent/50',
                'group',
                currentThreadId === id && 'bg-accent/70 border-l-2 border-l-primary'
              )}
            >
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <h3 className="text-sm font-medium text-foreground line-clamp-2 group-hover:text-primary transition-colors">
                    {title}
                  </h3>
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground ml-2 flex-shrink-0">
                    <Calendar className="w-3 h-3" />
                    <span>{date}</span>
                  </div>
                </div>

                <p className="text-xs text-muted-foreground line-clamp-2">
                  {preview}
                </p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {!isLoadingThreads && threads.length === 0 && mockConversations.length === 0 && (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <MessageSquare className="w-16 h-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">
            No conversations yet
          </h3>
          <p className="text-muted-foreground max-w-md mb-6">
            Start a new conversation with DeepLedger AI to manage your accounting tasks, 
            record transactions, and get financial insights.
          </p>
          <Button onClick={handleNewChat} className="bg-primary hover:bg-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            Start New Conversation
          </Button>
        </div>
      )}
    </div>
  );
}
