import React from 'react';
import {
  LayoutDashboard,
  MessageSquare,
  Receipt,
  FileText,
  Settings,
  Building2,
  Bot,
  Network,
  Server,
  Workflow,
  Globe,
  Menu,
  X,
  FileCode
} from 'lucide-react';
import { Button } from '../ui/Button';
import { OrganizationSwitcher } from '../ui/OrganizationSwitcher';
import { useAppStore, type ViewType } from '../../stores/app-store';
import { useAuthStore } from '../../stores/auth-store';
import { cn } from '../../utils/cn';

interface LeftSidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const agentsItems: Array<{ id: ViewType; label: string; icon: any }> = [
  { id: 'chat', label: 'AI Assistant', icon: MessageSquare },
  { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
];

const networkItems = [
  { label: 'Networks', icon: Network },
  { label: 'Connections', icon: Globe },
];

const toolsItems: Array<{ id?: ViewType; label: string; icon: any }> = [
  { id: 'transactions', label: 'Transactions', icon: Receipt },
  { id: 'reports', label: 'Reports', icon: FileText },
  { id: 'organization', label: 'My Organization', icon: Building2 },
  { id: 'settings', label: 'Settings', icon: Settings },
];

const serverItems = [
  { label: 'MCP Servers', icon: Server },
  { label: 'Workflows', icon: Workflow },
  { label: 'Runtime Context', icon: FileCode },
];

export function LeftSidebar({ collapsed, onToggleCollapse }: LeftSidebarProps) {
  const { currentView, setCurrentView } = useAppStore();
  const { user } = useAuthStore();

  const renderNavItem = (item: any, isActive?: boolean) => (
    <Button
      key={item.id || item.label}
      variant={isActive ? 'default' : 'ghost'}
      size="sm"
      className={cn(
        'w-full justify-start space-x-3 h-10',
        collapsed && 'px-2 justify-center',
        isActive && 'bg-accent text-accent-foreground'
      )}
      onClick={() => item.id && setCurrentView(item.id)}
    >
      <item.icon className="w-4 h-4 flex-shrink-0" />
      {!collapsed && <span className="truncate">{item.label}</span>}
    </Button>
  );

  const renderSection = (title: string, items: any[], showActive = true) => (
    <div className="space-y-1">
      {!collapsed && (
        <h3 className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
          {title}
        </h3>
      )}
      {items.map((item) => {
        const isActive = showActive && item.id && currentView === item.id;
        return renderNavItem(item, isActive);
      })}
    </div>
  );

  return (
    <div className="h-full bg-card flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div className="flex items-center space-x-2">
              <Bot className="w-6 h-6 text-primary" />
              <span className="font-semibold text-lg">Mastra</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="p-2"
          >
            {collapsed ? <Menu className="w-4 h-4" /> : <X className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Organization Switcher */}
      {user && (
        <div className="p-4 border-b border-border">
          {collapsed ? (
            <Button variant="outline" size="sm" className="w-full p-2">
              <Building2 className="w-4 h-4" />
            </Button>
          ) : (
            <OrganizationSwitcher />
          )}
        </div>
      )}

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {renderSection('Agents', agentsItems)}
        {renderSection('Networks', networkItems, false)}
        {renderSection('Tools', toolsItems)}
        {renderSection('MCP Servers', serverItems, false)}
        {renderSection('Workflows', [{ label: 'Workflows', icon: Workflow }], false)}
        {renderSection('Runtime Context', [{ label: 'Runtime Context', icon: FileCode }], false)}
      </div>

      {/* Bottom Section - Empty for now */}
      <div className="p-4 border-t border-border">
        {/* Reserved for future bottom navigation items */}
      </div>
    </div>
  );
}
