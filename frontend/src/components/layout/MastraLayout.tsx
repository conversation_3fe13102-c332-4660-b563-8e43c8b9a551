import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '../ui/Button';
import { LeftSidebar } from './LeftSidebar';
import { ConversationList } from './ConversationList';
import { useAppStore } from '../../stores/app-store';
import { ChatInterface } from '../chat/ChatInterface';
import { ProfilePage } from '../dashboard/ProfilePage';
import { DashboardOverview } from '../dashboard/DashboardOverview';
import { TransactionsView } from '../transactions/TransactionsView';
import { ReportsView } from '../reports/ReportsView';
import { SettingsView } from '../dashboard/SettingsView';
import { OrganizationManagement } from '../organization/OrganizationManagement';
import { SalesView } from '../accounting/SalesView';
import { ExpensesView } from '../accounting/ExpensesView';
import { ProductsServicesView } from '../accounting/ProductsServicesView';
import { ChartOfAccountsView } from '../accounting/ChartOfAccountsView';
import { GeneralLedgerView } from '../accounting/GeneralLedgerView';
import { BankReconciliationView } from '../accounting/BankReconciliationView';
import { MasterDataView } from '../accounting/MasterDataView';
import { BudgetForecastView } from '../accounting/BudgetForecastView';
import { ProcessAutomationView } from '../accounting/ProcessAutomationView';
import { cn } from '../../utils/cn';

export function MastraLayout() {
  const [leftSidebarCollapsed, setLeftSidebarCollapsed] = useState(false);
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(true); // Start collapsed
  const { currentView } = useAppStore();

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'transactions':
        return <TransactionsView />;
      case 'reports':
        return <ReportsView />;
      case 'profile':
        return <ProfilePage />;
      case 'settings':
        return <SettingsView />;
      case 'organization':
        return <OrganizationManagement />;
      case 'sales':
        return <SalesView />;
      case 'expenses':
        return <ExpensesView />;
      case 'products-services':
        return <ProductsServicesView />;
      case 'chart-accounts':
        return <ChartOfAccountsView />;
      case 'general-ledger':
        return <GeneralLedgerView />;
      case 'bank-reconciliation':
        return <BankReconciliationView />;
      case 'master-data':
        return <MasterDataView />;
      case 'budget-forecast':
        return <BudgetForecastView />;
      case 'process-automation':
        return <ProcessAutomationView />;
      case 'chat':
        return <ChatInterface />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Left Sidebar */}
      <div className={cn(
        'transition-all duration-300 ease-in-out border-r border-border flex-shrink-0',
        leftSidebarCollapsed ? 'w-16' : 'w-64'
      )}>
        <LeftSidebar
          collapsed={leftSidebarCollapsed}
          onToggleCollapse={() => setLeftSidebarCollapsed(!leftSidebarCollapsed)}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="h-14 bg-background border-b border-border px-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <ChevronLeft className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Agents</span>
              <span className="text-sm text-muted-foreground">/</span>
              <span className="text-sm font-medium">DeepLedger</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setRightSidebarCollapsed(!rightSidebarCollapsed)}
              className="p-2"
            >
              {rightSidebarCollapsed ? (
                <ChevronLeft className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>
          </div>
        </header>

        {/* Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Main Content */}
          <div className="flex-1 overflow-hidden">
            {currentView === 'chat' ? (
              <ConversationList />
            ) : (
              <div className="h-full p-6 overflow-auto">
                <div className="max-w-7xl mx-auto">
                  {renderContent()}
                </div>
              </div>
            )}
          </div>

          {/* Right Sidebar */}
          <div className={cn(
            'transition-all duration-300 ease-in-out border-l border-border bg-card flex-shrink-0',
            rightSidebarCollapsed ? 'w-0 overflow-hidden' : 'w-80'
          )}>
            {!rightSidebarCollapsed && (
              <div className="h-full p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold">Chat Options</h3>
                  </div>

                  {currentView === 'chat' ? (
                    <div className="space-y-2 text-sm">
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Chat Settings
                      </div>
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Export Conversation
                      </div>
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Clear History
                      </div>
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Download Transcript
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2 text-sm">
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Page Settings
                      </div>
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Export Data
                      </div>
                      <div className="p-2 rounded hover:bg-accent cursor-pointer">
                        Help & Support
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
