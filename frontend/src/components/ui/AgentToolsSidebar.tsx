import { DollarSign, TrendingDown, Package, FileText, BarChart3, BookOpen, Landmark, Database, TrendingUp, Zap } from 'lucide-react';
import { cn } from '../../utils/cn';
import { useAppStore, type ViewType } from '../../stores/app-store';

// Accounting tools configuration
const accountingTools = [
  { id: 'sales' as ViewType, name: 'Sales', icon: DollarSign, description: 'Manage sales and revenue' },
  { id: 'expenses' as ViewType, name: 'Expenses', icon: TrendingDown, description: 'Track and manage expenses' },
  { id: 'products-services' as ViewType, name: 'Product and Services', icon: Package, description: 'Manage products and services' },
  { id: 'reports' as ViewType, name: 'Reports', icon: FileText, description: 'Generate financial reports' },
  { id: 'chart-accounts' as ViewType, name: 'Chart of Accounts', icon: BarChart3, description: 'Manage account structure' },
  { id: 'general-ledger' as ViewType, name: 'General Ledger', icon: BookO<PERSON>, description: 'View general ledger entries' },
  { id: 'bank-reconciliation' as ViewType, name: 'Bank Reconciliation', icon: Landmark, description: 'Reconcile bank statements' },
  { id: 'master-data' as ViewType, name: 'Master Data', icon: Database, description: 'Manage master data' },
  { id: 'budget-forecast' as ViewType, name: 'Budget and Forecast', icon: TrendingUp, description: 'Create budgets and forecasts' },
  { id: 'process-automation' as ViewType, name: 'Process Automation', icon: Zap, description: 'Automate routine tasks' },
];

export function AgentToolsSidebar() {
  const { setCurrentView } = useAppStore();

  return (
    <div className="bg-card border-r border-border flex flex-col w-80 h-full">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <BarChart3 className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-lg font-semibold text-card-foreground">Accounting Tools</h2>
          </div>
        </div>
      </div>

      {/* Accounting Tools List */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {accountingTools.map((tool) => {
          const Icon = tool.icon;
          return (
            <div
              key={tool.id}
              className="group relative rounded-lg bg-muted/50 hover:bg-muted transition-all duration-200 cursor-pointer border border-border/50 hover:border-border hover:shadow-sm active:scale-95 p-3"
              onClick={() => setCurrentView(tool.id)}
            >
              <div className="flex items-start space-x-3">
                <Icon className="text-muted-foreground group-hover:text-primary transition-colors duration-200 w-5 h-5 mt-0.5" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-card-foreground group-hover:text-foreground transition-colors">
                    {tool.name}
                  </h3>
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                    {tool.description}
                  </p>
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-500/20 text-yellow-600 dark:text-yellow-400 border border-yellow-500/30">
                      Coming Soon
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <div className="text-xs text-muted-foreground text-center">
          <p>More tools coming soon!</p>
          <p className="mt-1">Powered by AI</p>
        </div>
      </div>
    </div>
  );
}
