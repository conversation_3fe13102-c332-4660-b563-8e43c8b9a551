import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { Button } from './Button';
import { cn } from '../../utils/cn';

export function ThemeToggle() {
  const { theme, setTheme, actualTheme } = useTheme();

  const themes = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'dark', icon: Moon, label: 'Dark' },
    { value: 'system', icon: Monitor, label: 'System' },
  ] as const;

  const currentTheme = themes.find(t => t.value === theme) || themes[2];
  const Icon = currentTheme.icon;

  const cycleTheme = () => {
    const currentIndex = themes.findIndex(t => t.value === theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex].value);
  };

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        onClick={cycleTheme}
        className={cn(
          'w-9 h-9 p-0 transition-all duration-200',
          'hover:bg-accent hover:text-accent-foreground',
          'focus:ring-2 focus:ring-ring focus:ring-offset-2',
          actualTheme === 'dark' 
            ? 'text-yellow-400 hover:text-yellow-300' 
            : 'text-gray-600 hover:text-gray-900'
        )}
        title={`Current theme: ${currentTheme.label}. Click to cycle themes.`}
      >
        <Icon className="h-4 w-4 transition-transform duration-200 hover:scale-110" />
        <span className="sr-only">Toggle theme</span>
      </Button>
      
      {/* Theme indicator */}
      <div className="absolute -bottom-1 -right-1 w-2 h-2 rounded-full bg-primary opacity-60" />
    </div>
  );
}

export function ThemeSelector() {
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'light', icon: Sun, label: 'Light' },
    { value: 'dark', icon: Moon, label: 'Dark' },
    { value: 'system', icon: Monitor, label: 'System' },
  ] as const;

  return (
    <div className="flex items-center space-x-1 p-1 bg-muted rounded-lg">
      {themes.map((themeOption) => {
        const Icon = themeOption.icon;
        const isActive = theme === themeOption.value;
        
        return (
          <Button
            key={themeOption.value}
            variant={isActive ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setTheme(themeOption.value)}
            className={cn(
              'w-8 h-8 p-0 transition-all duration-200',
              isActive && 'shadow-sm'
            )}
            title={themeOption.label}
          >
            <Icon className="h-4 w-4" />
            <span className="sr-only">{themeOption.label}</span>
          </Button>
        );
      })}
    </div>
  );
}
