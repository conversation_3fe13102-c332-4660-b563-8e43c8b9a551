import { useState } from 'react';
import {
  LayoutDashboard,
  MessageSquare,
  Receipt,
  FileText,
  Settings,
  User,
  Bot,
  Menu,
  X,
  Building2
} from 'lucide-react';
import { Button } from './Button';
import { ThemeToggle } from './ThemeToggle';
import { MobileNavigation } from './MobileNavigation';
import { OrganizationSwitcher } from './OrganizationSwitcher';
import { useAppStore, type ViewType } from '../../stores/app-store';
import { useChatStore } from '../../stores/chat-store';
import { useAuthStore } from '../../stores/auth-store';
import { cn } from '../../utils/cn';

const navigationItems: Array<{ id: ViewType; label: string; icon: any }> = [
  { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
  { id: 'chat', label: 'AI Assistant', icon: MessageSquare },
  { id: 'transactions', label: 'Transactions', icon: Receipt },
  { id: 'reports', label: 'Reports', icon: FileText },
  { id: 'organization', label: 'My Organization', icon: Building2 },
  { id: 'settings', label: 'Settings', icon: Settings },
];

export function Header() {
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const {
    currentView,
    setCurrentView
  } = useAppStore();
  const { isConnected } = useChatStore();
  const { user } = useAuthStore();

  return (
    <header className="bg-background border-b border-border px-4 py-3 transition-colors duration-200">
      <div className="flex items-center justify-between">
        {/* Left Section: Logo, Brand, and Mobile Menu */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden p-2"
            onClick={() => setMobileNavOpen(!mobileNavOpen)}
          >
            {mobileNavOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </Button>

          {/* Logo and Brand */}
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Bot className="w-8 h-8 text-primary transition-colors duration-200" />
              <div className={cn(
                "absolute -top-1 -right-1 w-3 h-3 rounded-full transition-colors duration-200",
                isConnected ? "bg-green-500 animate-pulse" : "bg-red-500"
              )} />
            </div>
            <div>
              <h1 className="text-xl font-bold text-foreground">DeepLedger</h1>
            </div>
          </div>
        </div>

        {/* Center Section: Navigation (Desktop) */}
        <nav className="hidden lg:flex items-center space-x-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;

            return (
              <Button
                key={item.id}
                variant={isActive ? 'default' : 'ghost'}
                size="sm"
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-all duration-200',
                  'hover:scale-105 active:scale-95',
                  isActive && 'shadow-sm'
                )}
                onClick={() => setCurrentView(item.id)}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden xl:inline">{item.label}</span>
                {item.id === 'chat' && !isConnected && (
                  <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                )}
              </Button>
            );
          })}
        </nav>

        {/* Right Section: Actions */}
        <div className="flex items-center space-x-2">
          {/* Organization Switcher - only show if user is logged in */}
          {user && (
            <div className="hidden md:block">
              <OrganizationSwitcher />
            </div>
          )}

          {/* Theme Toggle */}
          <ThemeToggle />

          {/* User Profile */}
          <Button
            variant="ghost"
            size="sm"
            className="p-2"
            onClick={() => setCurrentView('profile')}
          >
            <User className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <MobileNavigation
        isOpen={mobileNavOpen}
        onClose={() => setMobileNavOpen(false)}
      />
    </header>
  );
}
