import React from 'react';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Building2,
  CreditCard,
  PiggyBank,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Loader2
} from 'lucide-react';
import { Button } from './Button';
import { useFinancialMetricsStore } from '../../stores/financial-metrics-store';
import type { FinancialMetricItem } from '../../types/financial-metrics-simple';
import { TIME_PERIODS } from '../../types/financial-metrics-simple';
import { cn } from '../../utils/cn';

interface MetricCardProps {
  metric: FinancialMetricItem;
  isCollapsed: boolean;
}

function MetricCard({ metric, isCollapsed }: MetricCardProps) {
  const getIcon = (iconName: string) => {
    const icons = {
      'dollar-sign': DollarSign,
      'trending-up': TrendingUp,
      'trending-down': TrendingDown,
      'building2': Building2,
      'credit-card': CreditCard,
      'piggy-bank': PiggyBank,
    };
    return icons[iconName as keyof typeof icons] || DollarSign;
  };

  const Icon = getIcon(metric.icon);
  const isNegative = metric.value < 0;
  const formattedValue = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Math.abs(metric.value));

  if (isCollapsed) {
    return (
      <div className="p-2 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group relative">
        <div className="flex items-center justify-center">
          <Icon className={cn(
            "w-5 h-5",
            isNegative ? "text-red-500" : "text-green-500"
          )} />
        </div>
        {/* Tooltip on hover */}
        <div className="absolute left-full ml-2 top-0 bg-popover border border-border rounded-md p-2 shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-50 whitespace-nowrap">
          <div className="text-xs font-medium text-foreground">{metric.label}</div>
          <div className={cn(
            "text-sm font-bold",
            isNegative ? "text-red-500" : "text-green-500"
          )}>
            {isNegative ? '-' : ''}{formattedValue}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-3 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-all duration-200 hover:shadow-sm hover:scale-[1.02]">
      <div className="flex items-center justify-between mb-2">
        <Icon className={cn(
          "w-4 h-4 transition-colors duration-200",
          isNegative ? "text-red-500" : "text-green-500"
        )} />
        {metric.change !== undefined && (
          <span className={cn(
            "text-xs font-medium transition-colors duration-200",
            metric.changeType === 'positive' && "text-green-500",
            metric.changeType === 'negative' && "text-red-500",
            metric.changeType === 'neutral' && "text-muted-foreground"
          )}>
            {metric.change > 0 ? '+' : ''}{metric.change}%
          </span>
        )}
      </div>
      <div className="space-y-1">
        <p className="text-xs text-muted-foreground font-medium">{metric.label}</p>
        <p className={cn(
          "text-sm font-bold transition-colors duration-200",
          isNegative ? "text-red-500" : "text-green-500"
        )}>
          {isNegative ? '-' : ''}{formattedValue}
        </p>
      </div>
    </div>
  );
}

export function FinancialMetricsSidebar() {
  const {
    metrics,
    selectedPeriod,
    isLoading,
    error,
    isCollapsed,
    setSelectedPeriod,
    setCollapsed,
  } = useFinancialMetricsStore();

  const financialMetrics: FinancialMetricItem[] = metrics ? [
    {
      id: 'net-income',
      label: 'Net Income/Loss',
      value: metrics.netIncome,
      change: 12.5,
      changeType: metrics.netIncome >= 0 ? 'positive' : 'negative',
      icon: metrics.netIncome >= 0 ? 'trending-up' : 'trending-down',
      description: 'Total income minus total expenses',
    },
    {
      id: 'total-revenue',
      label: 'Total Revenue',
      value: metrics.totalRevenue,
      change: 8.3,
      changeType: 'positive',
      icon: 'dollar-sign',
      description: 'Total income from all sources',
    },
    {
      id: 'total-costs',
      label: 'Total Costs',
      value: metrics.totalCosts,
      change: -2.1,
      changeType: 'positive',
      icon: 'trending-down',
      description: 'Total expenses and costs',
    },
    {
      id: 'total-assets',
      label: 'Total Assets',
      value: metrics.totalAssets,
      change: 5.7,
      changeType: 'positive',
      icon: 'building2',
      description: 'Total value of assets owned',
    },
    {
      id: 'total-liabilities',
      label: 'Total Liabilities',
      value: metrics.totalLiabilities,
      change: -1.2,
      changeType: 'positive',
      icon: 'credit-card',
      description: 'Total debts and obligations',
    },
    {
      id: 'owners-equity',
      label: "Owner's Equity",
      value: metrics.ownersEquity,
      change: 15.8,
      changeType: 'positive',
      icon: 'piggy-bank',
      description: 'Assets minus liabilities',
    },
  ] : [];

  return (
    <div className={cn(
      "bg-card border-r border-border flex flex-col transition-all duration-300 ease-in-out",
      isCollapsed ? "w-16" : "w-80",
      "lg:relative absolute lg:translate-x-0 z-40 h-full",
      isCollapsed && "lg:translate-x-0 -translate-x-full lg:w-16"
    )}>
      {/* Sidebar Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className={cn(
            "flex items-center space-x-2 transition-opacity duration-200",
            isCollapsed && "lg:opacity-0 lg:pointer-events-none"
          )}>
            <DollarSign className="w-6 h-6 text-primary" />
            <div>
              <h2 className="text-lg font-semibold text-card-foreground">Financial Metrics</h2>
            </div>
          </div>

          {/* Collapse Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!isCollapsed)}
            className={cn(
              "hidden lg:flex p-1 h-8 w-8",
              isCollapsed && "mx-auto"
            )}
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      {!isCollapsed && (
        <div className="p-4 border-b border-border">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              Period
            </label>
            <select
              value={selectedPeriod.id}
              onChange={(e) => {
                const period = TIME_PERIODS.find(p => p.id === e.target.value);
                if (period) setSelectedPeriod(period);
              }}
              className="w-full p-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {TIME_PERIODS.map((period) => (
                <option key={period.id} value={period.id}>
                  {period.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Financial Metrics List */}
      <div className="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-3">
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
          </div>
        )}

        {error && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {!isLoading && !error && financialMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            metric={metric}
            isCollapsed={isCollapsed}
          />
        ))}
      </div>

      {/* Last Updated */}
      {!isCollapsed && metrics && (
        <div className="p-4 border-t border-border">
          <p className="text-xs text-muted-foreground">
            Last updated: {metrics.lastUpdated.toLocaleTimeString()}
          </p>
        </div>
      )}
    </div>
  );
}
