import { useState } from 'react';
import { useAuthStore } from '../../stores/auth-store';
import { Button } from './Button';
import { Card, CardContent } from './Card';
import { ChevronDown, Plus, Building2, Check } from 'lucide-react';
import { CreateOrganizationDialog } from './CreateOrganizationDialog';

// Component for organization logo with fallback
function OrganizationLogo({
  logo,
  name,
  size = 'sm'
}: {
  logo?: string | null;
  name: string;
  size?: 'sm' | 'md'
}) {
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6'
  };

  if (!logo || imageError) {
    return <Building2 className={`${sizeClasses[size]} text-muted-foreground flex-shrink-0`} />;
  }

  return (
    <img
      src={logo}
      alt={`${name} logo`}
      className={`${sizeClasses[size]} object-cover rounded border border-border flex-shrink-0 transition-opacity hover:opacity-90`}
      onError={() => setImageError(true)}
    />
  );
}

export function OrganizationSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const {
    organizations,
    currentOrganization,
    switchOrganization,
    organizationsLoading
  } = useAuthStore();



  if (organizationsLoading) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 bg-muted rounded-md">
        <Building2 className="w-4 h-4" />
        <span className="text-sm">Loading...</span>
      </div>
    );
  }

  if (organizations.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowCreateDialog(true)}
        className="flex items-center space-x-2"
      >
        <Plus className="w-4 h-4" />
        <span>Create Organization</span>
      </Button>
    );
  }

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between space-x-2 min-w-[200px]"
      >
        <div className="flex items-center space-x-2">
          <OrganizationLogo
            logo={currentOrganization?.logo}
            name={currentOrganization?.name || 'Organization'}
            size="sm"
          />
          <span className="truncate">
            {currentOrganization?.name || 'Select Organization'}
          </span>
        </div>
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {isOpen && (
        <Card className="absolute top-full left-0 mt-1 w-full min-w-[250px] z-50 shadow-lg">
          <CardContent className="p-2">
            <div className="space-y-1">
              {organizations.map((org) => (
                <button
                  key={org.organization_id}
                  onClick={() => {
                    switchOrganization(org);
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center justify-between p-2 text-left hover:bg-muted rounded-md transition-colors"
                >
                  <div className="flex items-center space-x-2">
                    <OrganizationLogo
                      logo={org.logo}
                      name={org.name}
                      size="md"
                    />
                    <div>
                      <div className="font-medium">{org.name}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {org.user_role}
                      </div>
                    </div>
                  </div>
                  {currentOrganization?.organization_id === org.organization_id && (
                    <Check className="w-4 h-4 text-primary" />
                  )}
                </button>
              ))}

              <hr className="my-2" />

              <button
                onClick={() => {
                  setShowCreateDialog(true);
                  setIsOpen(false);
                }}
                className="w-full flex items-center space-x-2 p-2 text-left hover:bg-muted rounded-md transition-colors"
              >
                <Plus className="w-6 h-6 text-muted-foreground flex-shrink-0" />
                <span className="font-medium">Create Organization</span>
              </button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      <CreateOrganizationDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
      />
    </div>
  );
}
