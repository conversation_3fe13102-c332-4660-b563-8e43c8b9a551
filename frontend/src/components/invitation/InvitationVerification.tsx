import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from '../ui/Button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { 
  Building2, 
  User, 
  Mail, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Loader2,
  UserPlus,
  LogIn
} from 'lucide-react';
import { useAuthStore } from '../../stores/auth-store';

interface InvitationData {
  invitation: {
    invitation_id: string;
    email: string;
    role: string;
    message?: string;
    invited_at: string;
    expires_at: string;
  };
  organization: {
    organization_id: string;
    name: string;
    logo?: string;
  };
  inviter: {
    full_name?: string;
    email: string;
  };
  user_exists: boolean;
}

export function InvitationVerification() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, signIn, refreshOrganizations } = useAuthStore();
  
  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accepting, setAccepting] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  
  // Sign up form state
  const [signUpData, setSignUpData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    full_name: ''
  });

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setError('Invalid invitation link');
      setLoading(false);
      return;
    }

    verifyInvitation();
  }, [token]);

  const verifyInvitation = async () => {
    try {
      console.log('🔍 Frontend: Verifying invitation token:', token);

      const response = await fetch(`http://localhost:3001/api/invitations/verify/${token}`);
      const data = await response.json();

      console.log('📡 Frontend: API Response:', { status: response.status, data });

      if (response.ok) {
        console.log('✅ Frontend: Invitation verified successfully');
        setInvitationData(data.data);
        setSignUpData(prev => ({ ...prev, email: data.data.invitation.email }));
      } else {
        console.error('❌ Frontend: Invitation verification failed:', data);

        // Provide more specific error messages
        let errorMessage = data.error || 'Invalid or expired invitation';
        if (errorMessage.includes('expired')) {
          errorMessage = 'This invitation has expired. Please request a new invitation from your organization administrator.';
        } else if (errorMessage.includes('already accepted')) {
          errorMessage = 'This invitation has already been accepted. If you need access, please contact your organization administrator.';
        } else if (errorMessage.includes('Invalid or expired')) {
          errorMessage = 'This invitation link is invalid or has expired. Please check the link or request a new invitation.';
        }

        setError(errorMessage);
      }
    } catch (error) {
      console.error('❌ Frontend: Network error verifying invitation:', error);
      setError('Failed to verify invitation. Please check your internet connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    console.log('🔍 Frontend: handleAcceptInvitation called');
    console.log('🔍 Frontend: User state:', user);
    console.log('🔍 Frontend: Invitation data:', invitationData);

    if (!user) {
      console.log('❌ Frontend: No user found, redirecting to sign in');
      setError('Please sign in to accept this invitation');
      return;
    }

    // Get email from user object or invitation data
    const userEmail = user.email || invitationData?.invitation?.email;

    console.log('🔍 Frontend: Email resolution:', {
      userEmail: user.email,
      invitationEmail: invitationData?.invitation?.email,
      finalEmail: userEmail
    });

    if (!userEmail) {
      console.log('❌ Frontend: No email found in user or invitation data');
      console.log('🔍 Frontend: User object keys:', Object.keys(user));
      setError('Unable to determine email address. Please sign in again.');
      return;
    }

    setAccepting(true);
    try {
      console.log('🔍 Frontend: Accepting invitation for email:', userEmail);
      console.log('🔍 Frontend: User object:', user);
      console.log('🔍 Frontend: Token:', token);

      const requestBody = {
        email: userEmail
      };
      console.log('🔍 Frontend: Request body:', requestBody);

      // Use the public endpoint which is more reliable
      const response = await fetch(`http://localhost:3001/api/invitations/accept-public/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      console.log('📡 Frontend: Accept invitation response:', { status: response.status, data });

      if (response.ok) {
        console.log('✅ Frontend: Invitation accepted successfully');

        // Refresh the auth store to get updated organization data
        await refreshOrganizations();

        // Redirect to organization dashboard
        navigate('/dashboard?view=organization');
      } else {
        console.error('❌ Frontend: Failed to accept invitation:', data);
        setError(data.error || 'Failed to accept invitation');
      }
    } catch (error) {
      console.error('❌ Frontend: Network error accepting invitation:', error);
      setError('Failed to accept invitation. Please check your internet connection and try again.');
    } finally {
      setAccepting(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (signUpData.password !== signUpData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (signUpData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setAccepting(true);
    try {
      // First, register the user
      const registerResponse = await fetch('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: signUpData.email,
          password: signUpData.password,
          full_name: signUpData.full_name,
        }),
      });

      const registerData = await registerResponse.json();

      if (registerResponse.ok) {
        console.log('✅ Frontend: User registration successful');

        // Auto sign in the user
        await signIn(signUpData.email, signUpData.password);

        // Accept the invitation using public endpoint (more reliable than waiting for auth)
        try {
          const acceptResponse = await fetch(`http://localhost:3001/api/invitations/accept-public/${token}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: signUpData.email
            }),
          });

          const acceptData = await acceptResponse.json();
          console.log('📡 Frontend: Accept invitation after signup response:', { status: acceptResponse.status, data: acceptData });

          if (acceptResponse.ok) {
            console.log('✅ Frontend: Invitation accepted after signup');
            // Redirect to organization dashboard
            navigate('/dashboard?view=organization');
          } else {
            console.error('❌ Frontend: Failed to accept invitation after signup:', acceptData);
            setError(acceptData.error || 'Account created but failed to accept invitation. Please try accepting the invitation again.');
          }
        } catch (acceptError) {
          console.error('❌ Frontend: Network error accepting invitation after signup:', acceptError);
          setError('Account created but failed to accept invitation. Please try accepting the invitation again.');
        }
      } else {
        setError(registerData.error || 'Failed to create account');
      }
    } catch (error) {
      console.error('Error during sign up:', error);
      setError('Failed to create account');
    } finally {
      setAccepting(false);
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Can manage organization settings, invite users, and access all features';
      case 'member':
        return 'Can view and edit organization data, create transactions and reports';
      case 'viewer':
        return 'Can view organization data and reports (read-only access)';
      default:
        return 'Organization member';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Verifying Invitation</h3>
            <p className="text-muted-foreground">Please wait while we verify your invitation...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Invalid Invitation</h3>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={() => navigate('/')} variant="outline">
              Go to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitationData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mail className="w-8 h-8 text-primary" />
            </div>
            <h1 className="text-3xl font-bold mb-2">You're Invited!</h1>
            <p className="text-muted-foreground">
              Join {invitationData.organization.name} on DeepLedger
            </p>
          </div>

          {/* Invitation Details */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {invitationData.organization.logo ? (
                  <img 
                    src={invitationData.organization.logo} 
                    alt={`${invitationData.organization.name} logo`}
                    className="w-8 h-8 object-cover rounded"
                  />
                ) : (
                  <Building2 className="w-8 h-8" />
                )}
                <span>{invitationData.organization.name}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-3">
                <User className="w-5 h-5 mt-0.5 text-muted-foreground" />
                <div>
                  <p className="font-medium">
                    Invited by {invitationData.inviter.full_name || invitationData.inviter.email}
                  </p>
                  <p className="text-sm text-muted-foreground">{invitationData.inviter.email}</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Shield className="w-5 h-5 mt-0.5 text-muted-foreground" />
                <div>
                  <p className="font-medium capitalize">{invitationData.invitation.role}</p>
                  <p className="text-sm text-muted-foreground">
                    {getRoleDescription(invitationData.invitation.role)}
                  </p>
                </div>
              </div>

              {invitationData.invitation.message && (
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-sm font-medium mb-1">Personal Message:</p>
                  <p className="text-sm italic">"{invitationData.invitation.message}"</p>
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                Invitation expires on {formatDate(invitationData.invitation.expires_at)}
              </div>
            </CardContent>
          </Card>

          {/* Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <Card className="mb-4 border-yellow-200 bg-yellow-50">
              <CardContent className="p-4">
                <h4 className="text-sm font-medium text-yellow-800 mb-2">Debug Info:</h4>
                <div className="text-xs text-yellow-700 space-y-1">
                  <div>User: {user ? 'Signed In' : 'Not Signed In'}</div>
                  {user && (
                    <>
                      <div>Email: {user.email || 'No email'}</div>
                      <div>ID: {user.id}</div>
                      <div>Full Name: {user.full_name || 'No name'}</div>
                    </>
                  )}
                  <div>Invitation Email: {invitationData?.invitation?.email}</div>
                  <div>User Exists: {invitationData?.user_exists ? 'Yes' : 'No'}</div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          {user ? (
            // User is signed in
            <Card>
              <CardContent className="p-6 text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Ready to Join</h3>
                <p className="text-muted-foreground mb-6">
                  You're signed in as {user.email || 'Unknown'}. Click below to accept this invitation.
                </p>
                <Button 
                  onClick={handleAcceptInvitation} 
                  disabled={accepting}
                  className="w-full"
                >
                  {accepting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Accepting Invitation...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Accept Invitation
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ) : (
            // User is not signed in
            <div className="space-y-4">
              {invitationData.user_exists ? (
                // User exists, show sign in
                <Card>
                  <CardContent className="p-6 text-center">
                    <LogIn className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Sign In Required</h3>
                    <p className="text-muted-foreground mb-6">
                      You already have an account. Please sign in to accept this invitation.
                    </p>
                    <Button
                      onClick={() => navigate(`/auth/signin?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`)}
                      className="w-full"
                    >
                      <LogIn className="w-4 h-4 mr-2" />
                      Sign In to Accept Invitation
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                // User doesn't exist, show sign up option
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center mb-6">
                      <UserPlus className="w-12 h-12 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">Create Your Account</h3>
                      <p className="text-muted-foreground">
                        Create a new account to join {invitationData.organization.name}
                      </p>
                    </div>

                    {!showSignUp ? (
                      <div className="space-y-3">
                        <Button
                          onClick={() => setShowSignUp(true)}
                          className="w-full"
                        >
                          <UserPlus className="w-4 h-4 mr-2" />
                          Create Account & Accept Invitation
                        </Button>
                        <Button
                          onClick={() => navigate(`/auth/signin?redirect=${encodeURIComponent(window.location.pathname + window.location.search)}`)}
                          variant="outline"
                          className="w-full"
                        >
                          <LogIn className="w-4 h-4 mr-2" />
                          I Already Have an Account
                        </Button>

                        {/* Quick Accept Option */}
                        <div className="relative">
                          <div className="absolute inset-0 flex items-center">
                            <span className="w-full border-t border-border" />
                          </div>
                          <div className="relative flex justify-center text-xs uppercase">
                            <span className="bg-background px-2 text-muted-foreground">Or</span>
                          </div>
                        </div>

                        <Button
                          onClick={async () => {
                            setAccepting(true);
                            try {
                              const response = await fetch(`http://localhost:3001/api/invitations/accept-public/${token}`, {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                  email: invitationData.invitation.email
                                }),
                              });

                              const data = await response.json();

                              if (response.ok) {
                                if (data.data.requires_registration) {
                                  setError('You need to create an account first. Please use the "Create Account" option above.');
                                } else {
                                  // User exists and was added to organization
                                  navigate('/auth/signin?message=Invitation accepted! Please sign in to access your organization.');
                                }
                              } else {
                                setError(data.error || 'Failed to accept invitation');
                              }
                            } catch (error) {
                              console.error('Error in quick accept:', error);
                              setError('Failed to accept invitation');
                            } finally {
                              setAccepting(false);
                            }
                          }}
                          variant="secondary"
                          className="w-full"
                          disabled={accepting}
                        >
                          {accepting ? (
                            <>
                              <Loader2 className="w-4 h-4 animate-spin mr-2" />
                              Accepting...
                            </>
                          ) : (
                            <>
                              <CheckCircle className="w-4 h-4 mr-2" />
                              Quick Accept (if you have an account)
                            </>
                          )}
                        </Button>
                      </div>
                    ) : (
                      <form onSubmit={handleSignUp} className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">Email</label>
                          <input
                            type="email"
                            value={signUpData.email}
                            onChange={(e) => setSignUpData(prev => ({ ...prev, email: e.target.value }))}
                            className="w-full p-2 border border-border bg-background text-foreground rounded-md"
                            required
                            disabled
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Full Name</label>
                          <input
                            type="text"
                            value={signUpData.full_name}
                            onChange={(e) => setSignUpData(prev => ({ ...prev, full_name: e.target.value }))}
                            className="w-full p-2 border border-border bg-background text-foreground rounded-md"
                            placeholder="Enter your full name"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Password</label>
                          <input
                            type="password"
                            value={signUpData.password}
                            onChange={(e) => setSignUpData(prev => ({ ...prev, password: e.target.value }))}
                            className="w-full p-2 border border-border bg-background text-foreground rounded-md"
                            placeholder="Enter your password"
                            required
                            minLength={8}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">Confirm Password</label>
                          <input
                            type="password"
                            value={signUpData.confirmPassword}
                            onChange={(e) => setSignUpData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            className="w-full p-2 border border-border bg-background text-foreground rounded-md"
                            placeholder="Confirm your password"
                            required
                          />
                        </div>

                        <div className="flex space-x-3">
                          <Button 
                            type="submit" 
                            disabled={accepting}
                            className="flex-1"
                          >
                            {accepting ? (
                              <>
                                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                Creating Account...
                              </>
                            ) : (
                              <>
                                <UserPlus className="w-4 h-4 mr-2" />
                                Create Account
                              </>
                            )}
                          </Button>
                          <Button 
                            type="button"
                            variant="outline"
                            onClick={() => setShowSignUp(false)}
                            disabled={accepting}
                          >
                            Cancel
                          </Button>
                        </div>
                      </form>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
