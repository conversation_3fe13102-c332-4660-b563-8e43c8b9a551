
import React from 'react';
import { Header } from '../ui/Header';
import { AgentToolsSidebar } from '../ui/AgentToolsSidebar';
import { useAppStore } from '../../stores/app-store';
import { ChatInterface } from '../chat/ChatInterface';
import { ProfilePage } from './ProfilePage';
import { DashboardOverview } from './DashboardOverview';
import { TransactionsView } from '../transactions/TransactionsView';
import { ReportsView } from '../reports/ReportsView';
import { SettingsView } from './SettingsView';
import { OrganizationManagement } from '../organization/OrganizationManagement';
import { SalesView } from '../accounting/SalesView';
import { ExpensesView } from '../accounting/ExpensesView';
import { ProductsServicesView } from '../accounting/ProductsServicesView';
import { ChartOfAccountsView } from '../accounting/ChartOfAccountsView';
import { GeneralLedgerView } from '../accounting/GeneralLedgerView';
import { BankReconciliationView } from '../accounting/BankReconciliationView';
import { MasterDataView } from '../accounting/MasterDataView';
import { BudgetForecastView } from '../accounting/BudgetForecastView';
import { ProcessAutomationView } from '../accounting/ProcessAutomationView';

export function DashboardLayout() {
  const { currentView } = useAppStore();

  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'transactions':
        return <TransactionsView />;
      case 'reports':
        return <ReportsView />;
      case 'profile':
        return <ProfilePage />;
      case 'settings':
        return <SettingsView />;
      case 'organization':
        return <OrganizationManagement />;
      case 'sales':
        return <SalesView />;
      case 'expenses':
        return <ExpensesView />;
      case 'products-services':
        return <ProductsServicesView />;
      case 'chart-accounts':
        return <ChartOfAccountsView />;
      case 'general-ledger':
        return <GeneralLedgerView />;
      case 'bank-reconciliation':
        return <BankReconciliationView />;
      case 'master-data':
        return <MasterDataView />;
      case 'budget-forecast':
        return <BudgetForecastView />;
      case 'process-automation':
        return <ProcessAutomationView />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground transition-colors duration-200">
      {/* Header Navigation */}
      <Header />

      {/* Main Layout with Sidebar */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Agent Tools Sidebar */}
        <AgentToolsSidebar />

        {/* Main Content Area */}
        <main className="flex-1 overflow-hidden relative">
          {currentView === 'chat' ? (
            <ChatInterface />
          ) : currentView === 'organization' ? (
            <div className="h-full bg-background text-foreground overflow-hidden animate-fade-in">
              {renderContent()}
            </div>
          ) : (
            <div className="h-full p-6 bg-background text-foreground overflow-auto animate-fade-in">
              <div className="max-w-7xl mx-auto">
                {renderContent()}
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}