import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Receipt,
  FileText,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { useAppStore } from '../../stores/app-store';
import { cn } from '../../utils/cn';

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
}

function StatCard({ title, value, change, changeType, icon: Icon }: StatCardProps) {
  return (
    <Card className="p-6 hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground">{value}</p>
          <div className={cn(
            "flex items-center text-sm mt-1",
            changeType === 'positive' && "text-green-600 dark:text-green-400",
            changeType === 'negative' && "text-red-600 dark:text-red-400",
            changeType === 'neutral' && "text-muted-foreground"
          )}>
            {changeType === 'positive' && <ArrowUpRight className="w-4 h-4 mr-1" />}
            {changeType === 'negative' && <ArrowDownRight className="w-4 h-4 mr-1" />}
            {change}
          </div>
        </div>
        <div className={cn(
          "p-3 rounded-full",
          changeType === 'positive' && "bg-green-100 dark:bg-green-900/20",
          changeType === 'negative' && "bg-red-100 dark:bg-red-900/20",
          changeType === 'neutral' && "bg-muted"
        )}>
          <Icon className={cn(
            "w-6 h-6",
            changeType === 'positive' && "text-green-600 dark:text-green-400",
            changeType === 'negative' && "text-red-600 dark:text-red-400",
            changeType === 'neutral' && "text-muted-foreground"
          )} />
        </div>
      </div>
    </Card>
  );
}

export function DashboardOverview() {
  const { setCurrentView } = useAppStore();

  const stats = [
    {
      title: 'Total Revenue',
      value: '$12,345',
      change: '+12.5% from last month',
      changeType: 'positive' as const,
      icon: DollarSign,
    },
    {
      title: 'Expenses',
      value: '$8,234',
      change: '-3.2% from last month',
      changeType: 'positive' as const,
      icon: TrendingDown,
    },
    {
      title: 'Net Profit',
      value: '$4,111',
      change: '+18.7% from last month',
      changeType: 'positive' as const,
      icon: TrendingUp,
    },
    {
      title: 'Transactions',
      value: '156',
      change: '+23 this month',
      changeType: 'neutral' as const,
      icon: Receipt,
    },
  ];

  const recentActivities = [
    { id: 1, type: 'income', description: 'Payment received from Client A', amount: '+$2,500', time: '2 hours ago' },
    { id: 2, type: 'expense', description: 'Office supplies purchase', amount: '-$150', time: '4 hours ago' },
    { id: 3, type: 'income', description: 'Consulting fee', amount: '+$1,200', time: '1 day ago' },
    { id: 4, type: 'expense', description: 'Software subscription', amount: '-$99', time: '2 days ago' },
  ];



  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Welcome back!</h1>
          <p className="text-muted-foreground mt-1">Here's what's happening with your business today.</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => setCurrentView('chat')} className="bg-primary hover:bg-primary/90">
            Ask AI Assistant
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-foreground">Recent Activity</h3>
            <Button variant="ghost" size="sm" onClick={() => setCurrentView('transactions')}>
              View All
            </Button>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    activity.type === 'income' ? "bg-green-500" : "bg-red-500"
                  )} />
                  <div>
                    <p className="text-sm font-medium text-foreground">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </div>
                <span className={cn(
                  "text-sm font-medium",
                  activity.type === 'income' ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
                )}>
                  {activity.amount}
                </span>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              className="h-20 flex flex-col space-y-2"
              onClick={() => setCurrentView('chat')}
            >
              <Receipt className="w-6 h-6" />
              <span className="text-sm">Record Transaction</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col space-y-2"
              onClick={() => setCurrentView('reports')}
            >
              <FileText className="w-6 h-6" />
              <span className="text-sm">Generate Report</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col space-y-2"
              onClick={() => setCurrentView('transactions')}
            >
              <TrendingUp className="w-6 h-6" />
              <span className="text-sm">View Analytics</span>
            </Button>
            <Button
              variant="outline"
              className="h-20 flex flex-col space-y-2"
              onClick={() => setCurrentView('settings')}
            >
              <Users className="w-6 h-6" />
              <span className="text-sm">Settings</span>
            </Button>
          </div>
        </Card>
      </div>

      {/* AI Assistant Suggestion */}
      <Card className="p-6 bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
        <div className="flex items-start space-x-4">
          <div className="p-2 bg-primary/20 rounded-full">
            <CheckCircle className="w-6 h-6 text-primary" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-foreground">AI Assistant Ready</h3>
            <p className="text-muted-foreground mt-1">
              Your AI accounting assistant is ready to help you record transactions, generate reports, and answer questions about your finances.
            </p>
            <Button
              className="mt-3"
              onClick={() => setCurrentView('chat')}
            >
              Start Conversation
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
