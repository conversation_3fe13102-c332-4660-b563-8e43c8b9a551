import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { ThemeToggle } from '../ui/ThemeToggle';
import {
  User,
  Shield,
  Database,
  Download,
  Trash2,
  Save
} from 'lucide-react';

export function SettingsView() {
  const [businessInfo, setBusinessInfo] = useState({
    businessName: '',
    businessType: '',
    taxId: '',
    address: ''
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
          <p className="text-muted-foreground mt-1">
            Manage your account and application preferences
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center space-x-3 p-2 rounded-md bg-primary/10 text-primary">
                <User className="h-4 w-4" />
                <span className="text-sm font-medium">Account</span>
              </div>

              <div className="flex items-center space-x-3 p-2 rounded-md hover:bg-muted cursor-pointer">
                <Shield className="h-4 w-4" />
                <span className="text-sm">Security</span>
              </div>
              <div className="flex items-center space-x-3 p-2 rounded-md hover:bg-muted cursor-pointer">
                <Database className="h-4 w-4" />
                <span className="text-sm">Data & Privacy</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Account Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Account Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Business Name
                  </label>
                  <Input
                    placeholder="Enter business name"
                    value={businessInfo.businessName}
                    onChange={(e) => setBusinessInfo(prev => ({
                      ...prev,
                      businessName: e.target.value
                    }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Business Type
                  </label>
                  <select className="w-full p-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option>Select business type</option>
                    <option>Sole Proprietorship</option>
                    <option>LLC</option>
                    <option>Corporation</option>
                    <option>Partnership</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Tax ID / EIN
                  </label>
                  <Input
                    placeholder="XX-XXXXXXX"
                    value={businessInfo.taxId}
                    onChange={(e) => setBusinessInfo(prev => ({
                      ...prev,
                      taxId: e.target.value
                    }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Business Address
                  </label>
                  <Input
                    placeholder="Enter business address"
                    value={businessInfo.address}
                    onChange={(e) => setBusinessInfo(prev => ({
                      ...prev,
                      address: e.target.value
                    }))}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Appearance Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-foreground">Theme</h4>
                  <p className="text-sm text-muted-foreground">
                    Choose your preferred theme
                  </p>
                </div>
                <ThemeToggle />
              </div>
            </CardContent>
          </Card>


          {/* Data & Privacy */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Data & Privacy</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-foreground">Export Data</h4>
                  <p className="text-sm text-muted-foreground">
                    Download all your data in JSON format
                  </p>
                </div>
                <Button variant="outline" disabled>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-foreground">Delete Account</h4>
                  <p className="text-sm text-muted-foreground">
                    Permanently delete your account and all data
                  </p>
                </div>
                <Button variant="destructive" disabled>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
