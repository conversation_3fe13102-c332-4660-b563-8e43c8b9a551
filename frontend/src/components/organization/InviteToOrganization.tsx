import { useState, useRef, useEffect } from 'react';
import { Button } from '../ui/Button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { UserPlus, Search, X, User, Check, AlertCircle } from 'lucide-react';
import { tokenManager } from '../../services/api-client';

interface UserSearchResult {
  id: string;
  email: string;
  first_name?: string | null;
  last_name?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  is_member?: boolean;
}

interface InviteToOrganizationProps {
  organizationId: string;
}

interface Role {
  role_id: string;
  name: string;
  display_name: string;
  description?: string;
  permissions: string[];
  is_system_role: boolean;
}

export function InviteToOrganization({ organizationId }: InviteToOrganizationProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
  const [role, setRole] = useState<string>('member');
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(true);
  const [message, setMessage] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isInviting, setIsInviting] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [inviteStatus, setInviteStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });

  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Helper function to get auth headers
  const getAuthHeaders = (): Record<string, string> => {
    const token = tokenManager.getAccessToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  };

  // Fetch available roles on component mount
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setIsLoadingRoles(true);
        const authHeaders = getAuthHeaders();

        const response = await fetch('http://localhost:3001/api/roles/system', {
          headers: authHeaders,
        });

        if (response.ok) {
          const data = await response.json();
          const systemRoles = data.data || [];

          // Filter out owner role from invitation options (only admins+ can assign)
          const invitableRoles = systemRoles.filter((role: Role) => role.name !== 'owner');
          setRoles(invitableRoles);

          // Set default role to member if available
          const memberRole = invitableRoles.find((role: Role) => role.name === 'member');
          if (memberRole) {
            setRole(memberRole.name);
          } else if (invitableRoles.length > 0) {
            setRole(invitableRoles[0].name);
          }
        } else {
          console.error('Failed to fetch roles:', response.status);
          // Fallback to hardcoded roles
          const fallbackRoles = [
            { role_id: '1', name: 'viewer', display_name: 'Viewer', description: 'Can view organization data', permissions: ['read'], is_system_role: true },
            { role_id: '2', name: 'member', display_name: 'Member', description: 'Can view and edit organization data', permissions: ['read', 'write'], is_system_role: true },
            { role_id: '3', name: 'accountant', display_name: 'Accountant', description: 'Can view and manage financial data', permissions: ['read', 'write', 'financial'], is_system_role: true },
            { role_id: '4', name: 'admin', display_name: 'Admin', description: 'Can manage organization and invite users', permissions: ['read', 'write', 'admin'], is_system_role: true },
          ];
          setRoles(fallbackRoles);
          setRole('member');
        }
      } catch (error) {
        console.error('Error fetching roles:', error);
        // Fallback to hardcoded roles
        const fallbackRoles = [
          { role_id: '1', name: 'viewer', display_name: 'Viewer', description: 'Can view organization data', permissions: ['read'], is_system_role: true },
          { role_id: '2', name: 'member', display_name: 'Member', description: 'Can view and edit organization data', permissions: ['read', 'write'], is_system_role: true },
          { role_id: '3', name: 'accountant', display_name: 'Accountant', description: 'Can view and manage financial data', permissions: ['read', 'write', 'financial'], is_system_role: true },
          { role_id: '4', name: 'admin', display_name: 'Admin', description: 'Can manage organization and invite users', permissions: ['read', 'write', 'admin'], is_system_role: true },
        ];
        setRoles(fallbackRoles);
        setRole('member');
      } finally {
        setIsLoadingRoles(false);
      }
    };

    fetchRoles();
  }, []);



  // Debounced search function
  useEffect(() => {
    console.log('🔍 Search query changed:', searchQuery); // Debug log

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (searchQuery.trim().length >= 2) {
      console.log('🔍 Setting up search timeout for:', searchQuery.trim()); // Debug log
      searchTimeoutRef.current = setTimeout(() => {
        console.log('🔍 Timeout triggered, calling performSearch'); // Debug log
        performSearch(searchQuery.trim());
      }, 300);
    } else {
      console.log('🔍 Query too short, clearing results'); // Debug log
      setSearchResults([]);
      setShowResults(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  const performSearch = async (query: string) => {
    setIsSearching(true);
    console.log('🔍 Performing search for:', query); // Debug log
    console.log('🔍 Query length:', query.length); // Debug log

    try {
      const authHeaders = getAuthHeaders();
      console.log('Auth headers:', authHeaders); // Debug log

      // For testing, try search without auth if no auth headers
      const hasAuth = authHeaders.Authorization !== undefined;
      console.log('Has auth token:', hasAuth); // Debug log

      const url = `http://localhost:3001/api/invitations/search?query=${encodeURIComponent(query)}&organization_id=${organizationId}&limit=10`;
      console.log('Search URL:', url); // Debug log

      const response = await fetch(url, {
        headers: authHeaders,
      });

      console.log('Response status:', response.status); // Debug log
      console.log('Response ok:', response.ok); // Debug log

      if (response.ok) {
        const data = await response.json();
        console.log('Search API response:', data); // Debug log
        console.log('Search results:', data.data); // Debug log

        // Use API results
        let results = data.data || [];
        console.log('Processed results:', results); // Debug log

        // If no results but query looks like email, create a suggestion
        if (results.length === 0 && query.includes('@') && query.includes('.')) {
          results = [{
            id: 'email-suggestion',
            email: query,
            first_name: null,
            last_name: null,
            full_name: null,
            avatar_url: null,
            is_member: false
          }];
          console.log('Added email suggestion:', results); // Debug log
        }

        setSearchResults(results);
        setShowResults(true);
      } else {
        const errorText = await response.text();
        console.error('Search failed:', response.status, response.statusText, errorText);
        // If API fails and query looks like email, show email suggestion
        if (query.includes('@') && query.includes('.')) {
          setSearchResults([{
            id: 'email-suggestion',
            email: query,
            first_name: null,
            last_name: null,
            full_name: null,
            avatar_url: null,
            is_member: false
          }]);
          setShowResults(true);
        } else {
          setSearchResults([]);
        }
      }
    } catch (error) {
      console.error('Search error:', error);
      // If network error and query looks like email, show email suggestion
      if (query.includes('@') && query.includes('.')) {
        setSearchResults([{
          id: 'email-suggestion',
          email: query,
          first_name: null,
          last_name: null,
          full_name: null,
          avatar_url: null,
          is_member: false
        }]);
        setShowResults(true);
      } else {
        setSearchResults([]);
      }
    } finally {
      setIsSearching(false);
    }
  };

  const handleUserSelect = (user: UserSearchResult) => {
    setSelectedUser(user);
    setSearchQuery(user.email);
    setShowResults(false);
  };

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleInvite = async () => {
    const email = selectedUser ? selectedUser.email : searchQuery.trim();

    // Validate email format
    if (!email || !validateEmail(email)) {
      setInviteStatus({
        type: 'error',
        message: 'Please enter a valid email address.'
      });
      return;
    }

    setIsInviting(true);
    setInviteStatus({ type: null, message: '' });

    try {
      const authHeaders = getAuthHeaders();
      console.log('Invitation auth headers:', authHeaders); // Debug log

      const response = await fetch(`http://localhost:3001/api/invitations/${organizationId}`, {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          email,
          role,
          message: message.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setInviteStatus({
          type: 'success',
          message: `🎉 Invitation sent successfully to ${email}! They will receive an email with instructions to join the organization.`
        });

        // Reset form
        setSearchQuery('');
        setSelectedUser(null);
        setMessage('');
        // Reset to default role (member if available, otherwise first role)
        const memberRole = roles.find(r => r.name === 'member');
        setRole(memberRole ? memberRole.name : (roles.length > 0 ? roles[0].name : 'member'));
        setSearchResults([]);
        setShowResults(false);
      } else {
        setInviteStatus({
          type: 'error',
          message: data.error || 'Failed to send invitation.'
        });
      }
    } catch (error) {
      console.error('Invite error:', error);
      setInviteStatus({
        type: 'error',
        message: 'An error occurred while sending the invitation.'
      });
    } finally {
      setIsInviting(false);
    }
  };

  const clearSelection = () => {
    setSelectedUser(null);
    setSearchQuery('');
    setSearchResults([]);
    setShowResults(false);
    searchInputRef.current?.focus();
  };

  const getUserDisplayName = (user: UserSearchResult) => {
    if (user.full_name) return user.full_name;
    if (user.first_name || user.last_name) {
      return [user.first_name, user.last_name].filter(Boolean).join(' ');
    }
    return user.email;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <UserPlus className="w-5 h-5" />
          <span>Send Invitation</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Messages */}
        {inviteStatus.type && (
          <div className={`p-4 rounded-md flex items-center space-x-2 ${
            inviteStatus.type === 'success'
              ? 'bg-green-50 text-green-800 border border-green-200'
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {inviteStatus.type === 'success' ? (
              <Check className="w-4 h-4" />
            ) : (
              <AlertCircle className="w-4 h-4" />
            )}
            <span className="text-sm">{inviteStatus.message}</span>
          </div>
        )}

        {/* User Search */}
        <div className="space-y-2">
          <label className="block text-sm font-medium">Email Address or Search User</label>
          <div className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Enter email address or search by name..."
                className="w-full pl-10 pr-10 py-2 border border-border bg-background text-foreground placeholder:text-muted-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              {(selectedUser || searchQuery) && (
                <button
                  onClick={clearSelection}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Search Results Dropdown */}
            {showResults && searchResults.length > 0 && (
              <div className="absolute z-50 w-full mt-1 bg-background border border-border rounded-md shadow-xl max-h-60 overflow-auto">
                <div className="px-3 py-2 text-xs text-muted-foreground border-b border-border bg-muted/30">
                  {searchResults.length} suggestion{searchResults.length !== 1 ? 's' : ''} found
                </div>
                {searchResults.map((user) => (
                  <button
                    key={user.id}
                    onClick={() => handleUserSelect(user)}
                    className="w-full px-4 py-3 text-left hover:bg-muted flex items-center space-x-3 border-b border-border last:border-b-0 text-foreground transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-4 h-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-foreground">{getUserDisplayName(user)}</div>
                      <div className="text-xs text-muted-foreground truncate">{user.email}</div>
                      {user.id === 'email-suggestion' && (
                        <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">Click to invite this email</div>
                      )}
                    </div>
                    {user.is_member && (
                      <div className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                        Member
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}

            {/* No Results */}
            {showResults && searchResults.length === 0 && !isSearching && searchQuery.length >= 2 && (
              <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg p-4 text-center text-muted-foreground">
                <div className="text-sm">No users found</div>
                <div className="text-xs mt-1">
                  {searchQuery.includes('@') ? 'You can still send an invitation to this email' : 'Try searching by email address'}
                </div>
              </div>
            )}

            {/* Loading */}
            {isSearching && (
              <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg p-4 text-center text-muted-foreground">
                <div className="text-sm">Searching...</div>
              </div>
            )}
          </div>
        </div>

        {/* Role Selection */}
        <div className="space-y-2">
          <label className="block text-sm font-medium">Role</label>
          {isLoadingRoles ? (
            <div className="w-full p-2 border border-border bg-background text-muted-foreground rounded-md">
              Loading roles...
            </div>
          ) : (
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="w-full p-2 border border-border bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {roles.map((roleOption) => (
                <option
                  key={roleOption.role_id}
                  value={roleOption.name}
                  className="text-foreground bg-background"
                >
                  {roleOption.display_name} - {roleOption.description || 'No description available'}
                </option>
              ))}
            </select>
          )}
        </div>

        {/* Optional Message */}
        <div className="space-y-2">
          <label className="block text-sm font-medium">Message (Optional)</label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Add a personal message to the invitation..."
            rows={3}
            maxLength={500}
            className="w-full p-2 border border-border bg-background text-foreground placeholder:text-muted-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
          />
          <div className="text-xs text-muted-foreground text-right">
            {message.length}/500 characters
          </div>
        </div>

        {/* Send Button */}
        <Button
          onClick={handleInvite}
          disabled={isInviting || !validateEmail(selectedUser?.email || searchQuery.trim())}
          className="w-full"
        >
          {isInviting ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Sending Invitation...
            </>
          ) : (
            <>
              <UserPlus className="w-4 h-4 mr-2" />
              Send Invitation
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
