import { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/auth-store';
import { organizationAPI, type OrganizationMember, type OrganizationMemberStats } from '../../services/organization-api';
import { Button } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import {
  Users,
  Search,
  MoreVertical,
  Shield,
  UserCheck,
  UserX,
  Crown,
  Eye,
  Settings,
  Trash2,
  Mail,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  AlertTriangle,
  CheckSquare,
  Square,
  UserMinus,
  LogOut,
  X
} from 'lucide-react';

interface OrganizationMembersProps {
  organizationId: string;
}

type RoleFilter = 'all' | 'owner' | 'admin' | 'member' | 'viewer';

const roleConfig = {
  owner: {
    label: 'Owner',
    icon: Crown,
    color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    description: 'Full access to organization'
  },
  admin: {
    label: 'Admin',
    icon: Shield,
    color: 'text-red-600 bg-red-50 border-red-200',
    description: 'Can manage users and settings'
  },
  member: {
    label: 'Member',
    icon: UserCheck,
    color: 'text-blue-600 bg-blue-50 border-blue-200',
    description: 'Can view and edit data'
  },
  viewer: {
    label: 'Viewer',
    icon: Eye,
    color: 'text-gray-600 bg-gray-50 border-gray-200',
    description: 'Read-only access'
  }
};

export function OrganizationMembers({ organizationId }: OrganizationMembersProps) {
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [stats, setStats] = useState<OrganizationMemberStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<RoleFilter>('all');
  const [selectedMembers, setSelectedMembers] = useState<Set<string>>(new Set());
  const [showActions, setShowActions] = useState<string | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [confirmRemoval, setConfirmRemoval] = useState<{
    show: boolean;
    member?: OrganizationMember;
    isBulk?: boolean;
    isLeaving?: boolean;
  }>({ show: false });
  const [removingUsers, setRemovingUsers] = useState<Set<string>>(new Set());

  const { currentOrganization, user } = useAuthStore();

  // Load members on component mount
  useEffect(() => {
    loadMembers();
  }, [organizationId]);

  const loadMembers = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load both members and stats in parallel
      const [membersData, statsData] = await Promise.all([
        organizationAPI.getOrganizationMembers(organizationId),
        organizationAPI.getOrganizationMemberStats(organizationId)
      ]);

      setMembers(membersData);
      setStats(statsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load members');
      console.error('Error loading members:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter members based on search and role filter
  const filteredMembers = members.filter(member => {
    const matchesSearch = searchQuery === '' || 
      member.user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (member.user.full_name && member.user.full_name.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    
    return matchesSearch && matchesRole;
  });

  // Use API stats if available, otherwise calculate from current members
  const roleStats = stats?.role_breakdown || members.reduce((acc, member) => {
    acc[member.role] = (acc[member.role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const handleRoleChange = async (memberId: string, newRole: string) => {
    try {
      const member = members.find(m => m.user_id === memberId);
      if (!member) return;

      await organizationAPI.updateUserRole(organizationId, memberId, { role: newRole as any });
      await loadMembers(); // Reload to get updated data
      setShowActions(null);
    } catch (err) {
      console.error('Error updating role:', err);
      setError(err instanceof Error ? err.message : 'Failed to update role');
    }
  };

  const handleRemoveMember = async (memberId: string, isLeaving: boolean = false) => {
    try {
      setRemovingUsers(prev => new Set(prev).add(memberId));

      if (isLeaving) {
        await organizationAPI.leaveOrganization(organizationId);
      } else {
        await organizationAPI.removeUserFromOrganization(organizationId, memberId);
      }

      await loadMembers(); // Reload to get updated data
      setShowActions(null);
      setConfirmRemoval({ show: false });
      setSelectedMembers(new Set()); // Clear selection
    } catch (err) {
      console.error('Error removing member:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove member');
    } finally {
      setRemovingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(memberId);
        return newSet;
      });
    }
  };

  const handleBulkRemove = async () => {
    try {
      const memberIds = Array.from(selectedMembers);
      setRemovingUsers(new Set(memberIds));

      // Remove members in parallel
      await Promise.all(
        memberIds.map(memberId =>
          organizationAPI.removeUserFromOrganization(organizationId, memberId)
        )
      );

      await loadMembers(); // Reload to get updated data
      setSelectedMembers(new Set());
      setConfirmRemoval({ show: false });
      setShowBulkActions(false);
    } catch (err) {
      console.error('Error removing members:', err);
      setError(err instanceof Error ? err.message : 'Failed to remove members');
    } finally {
      setRemovingUsers(new Set());
    }
  };

  const toggleMemberSelection = (memberId: string) => {
    setSelectedMembers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(memberId)) {
        newSet.delete(memberId);
      } else {
        newSet.add(memberId);
      }
      return newSet;
    });
  };

  const selectAllMembers = () => {
    const selectableMembers = filteredMembers.filter(member =>
      canManageUser(member) && member.user_id !== user?.id
    );
    setSelectedMembers(new Set(selectableMembers.map(m => m.user_id)));
  };

  const clearSelection = () => {
    setSelectedMembers(new Set());
  };

  const canManageUser = (member: OrganizationMember): boolean => {
    if (!currentOrganization || !user) return false;
    
    // Owners can manage everyone except other owners
    if (currentOrganization.user_role === 'owner') {
      return member.role !== 'owner' || member.user_id === user.id;
    }
    
    // Admins can manage members and viewers
    if (currentOrganization.user_role === 'admin') {
      return ['member', 'viewer'].includes(member.role);
    }
    
    return false;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 mx-auto text-muted-foreground mb-4 animate-spin" />
          <p className="text-muted-foreground">Loading members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">Organization Members</h2>
          <p className="text-muted-foreground">
            Manage members of {currentOrganization?.name} ({members.length} total)
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={loadMembers}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          {selectedMembers.size > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBulkActions(!showBulkActions)}
            >
              <UserMinus className="w-4 h-4 mr-2" />
              Bulk Actions ({selectedMembers.size})
            </Button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {/* Bulk Actions Toolbar */}
      {showBulkActions && selectedMembers.size > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <UserMinus className="w-5 h-5 text-orange-600" />
                  <span className="font-medium text-orange-800">
                    {selectedMembers.size} member{selectedMembers.size > 1 ? 's' : ''} selected
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={selectAllMembers}>
                    <CheckSquare className="w-4 h-4 mr-2" />
                    Select All
                  </Button>
                  <Button variant="outline" size="sm" onClick={clearSelection}>
                    <X className="w-4 h-4 mr-2" />
                    Clear
                  </Button>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => setConfirmRemoval({ show: true, isBulk: true })}
                  disabled={removingUsers.size > 0}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Remove Selected
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBulkActions(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Object.entries(roleConfig).map(([role, config]) => {
          const count = roleStats[role] || 0;
          const IconComponent = config.icon;
          return (
            <Card key={role} className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => setRoleFilter(role as RoleFilter)}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-md border ${config.color}`}>
                    <IconComponent className="w-4 h-4" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold">{count}</p>
                    <p className="text-sm text-muted-foreground">{config.label}s</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                placeholder="Search members by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-md bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            {/* Role Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as RoleFilter)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Roles</option>
                {Object.entries(roleConfig).map(([role, config]) => (
                  <option key={role} value={role}>{config.label}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Members List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Members ({filteredMembers.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredMembers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No members found</h3>
              <p className="text-muted-foreground">
                {searchQuery || roleFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'This organization has no members yet.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredMembers.map((member) => {
                const roleInfo = roleConfig[member.role as keyof typeof roleConfig];
                const RoleIcon = roleInfo?.icon || UserCheck;
                const canManage = canManageUser(member);
                const isCurrentUser = member.user_id === user?.id;

                return (
                  <div
                    key={member.user_id}
                    className={`flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors ${
                      selectedMembers.has(member.user_id) ? 'bg-blue-50 border-blue-200' : ''
                    } ${removingUsers.has(member.user_id) ? 'opacity-50' : ''}`}
                  >
                    {/* Selection Checkbox */}
                    {canManage && !isCurrentUser && (
                      <div className="flex items-center mr-3">
                        <button
                          onClick={() => toggleMemberSelection(member.user_id)}
                          className="p-1 hover:bg-muted rounded"
                          disabled={removingUsers.has(member.user_id)}
                        >
                          {selectedMembers.has(member.user_id) ? (
                            <CheckSquare className="w-4 h-4 text-blue-600" />
                          ) : (
                            <Square className="w-4 h-4 text-muted-foreground" />
                          )}
                        </button>
                      </div>
                    )}

                    {/* User Info */}
                    <div className="flex items-center space-x-4 flex-1">
                      {/* Avatar */}
                      <div className="relative">
                        {member.user.avatar_url ? (
                          <img
                            src={member.user.avatar_url}
                            alt={member.user.full_name || member.user.email}
                            className="w-10 h-10 rounded-full object-cover border border-border"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center border border-border">
                            <Users className="w-5 h-5 text-primary" />
                          </div>
                        )}
                        {isCurrentUser && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"></div>
                        )}
                      </div>

                      {/* User Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-foreground truncate">
                            {member.user.full_name || 'Unknown User'}
                            {isCurrentUser && (
                              <span className="text-xs text-muted-foreground ml-2">(You)</span>
                            )}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Mail className="w-3 h-3" />
                            <span className="truncate">{member.user.email}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>Joined {formatDate(member.created_at)}</span>
                          </div>
                        </div>
                      </div>

                      {/* Role Badge */}
                      <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border text-xs font-medium ${roleInfo?.color || 'text-gray-600 bg-gray-50 border-gray-200'}`}>
                        <RoleIcon className="w-3 h-3" />
                        <span>{roleInfo?.label || member.role}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    {(canManage || isCurrentUser) && (
                      <div className="relative ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowActions(showActions === member.user_id ? null : member.user_id)}
                          className="p-2"
                          disabled={removingUsers.has(member.user_id)}
                        >
                          {removingUsers.has(member.user_id) ? (
                            <RefreshCw className="w-4 h-4 animate-spin" />
                          ) : (
                            <MoreVertical className="w-4 h-4" />
                          )}
                        </Button>

                        {showActions === member.user_id && (
                          <div className="absolute right-0 top-full mt-1 w-48 bg-background border border-border rounded-md shadow-lg z-10">
                            <div className="py-1">
                              {/* Role Change Options - Only for non-current users */}
                              {canManage && !isCurrentUser && (
                                <>
                                  <div className="px-3 py-2 text-xs font-medium text-muted-foreground border-b border-border">
                                    Change Role
                                  </div>
                                  {Object.entries(roleConfig).map(([role, config]) => {
                                    if (role === member.role || (role === 'owner' && currentOrganization?.user_role !== 'owner')) {
                                      return null;
                                    }
                                    const IconComponent = config.icon;
                                    return (
                                      <button
                                        key={role}
                                        onClick={() => handleRoleChange(member.user_id, role)}
                                        className="w-full flex items-center space-x-2 px-3 py-2 text-sm hover:bg-muted text-left"
                                      >
                                        <IconComponent className="w-4 h-4" />
                                        <span>{config.label}</span>
                                      </button>
                                    );
                                  })}
                                </>
                              )}

                              {/* Remove/Leave Actions */}
                              <div className="border-t border-border my-1"></div>

                              {isCurrentUser ? (
                                <button
                                  onClick={() => setConfirmRemoval({
                                    show: true,
                                    member,
                                    isLeaving: true
                                  })}
                                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-orange-600 hover:bg-orange-50"
                                >
                                  <LogOut className="w-4 h-4" />
                                  <span>Leave organization</span>
                                </button>
                              ) : canManage && (
                                <button
                                  onClick={() => setConfirmRemoval({
                                    show: true,
                                    member
                                  })}
                                  className="w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                                >
                                  <Trash2 className="w-4 h-4" />
                                  <span>Remove from organization</span>
                                </button>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Click outside to close actions menu */}
      {showActions && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowActions(null)}
        />
      )}

      {/* Confirmation Dialog */}
      {confirmRemoval.show && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-red-500" />
                <span>
                  {confirmRemoval.isLeaving ? 'Leave Organization' :
                   confirmRemoval.isBulk ? 'Remove Multiple Members' : 'Remove Member'}
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                {confirmRemoval.isLeaving ? (
                  <>
                    <p>Are you sure you want to leave <strong>{currentOrganization?.name}</strong>?</p>
                    <p className="mt-2 text-red-600">
                      You will lose access to all organization data and will need to be re-invited to rejoin.
                    </p>
                  </>
                ) : confirmRemoval.isBulk ? (
                  <>
                    <p>Are you sure you want to remove <strong>{selectedMembers.size} member{selectedMembers.size > 1 ? 's' : ''}</strong> from <strong>{currentOrganization?.name}</strong>?</p>
                    <p className="mt-2 text-red-600">
                      They will lose access to all organization data and will need to be re-invited to rejoin.
                    </p>
                  </>
                ) : (
                  <>
                    <p>Are you sure you want to remove <strong>{confirmRemoval.member?.user.full_name || confirmRemoval.member?.user.email}</strong> from <strong>{currentOrganization?.name}</strong>?</p>
                    <p className="mt-2 text-red-600">
                      They will lose access to all organization data and will need to be re-invited to rejoin.
                    </p>
                  </>
                )}
              </div>

              <div className="flex items-center justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setConfirmRemoval({ show: false })}
                  disabled={removingUsers.size > 0}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    if (confirmRemoval.isBulk) {
                      handleBulkRemove();
                    } else if (confirmRemoval.member) {
                      handleRemoveMember(confirmRemoval.member.user_id, confirmRemoval.isLeaving);
                    }
                  }}
                  disabled={removingUsers.size > 0}
                >
                  {removingUsers.size > 0 ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      {confirmRemoval.isLeaving ? 'Leaving...' : 'Removing...'}
                    </>
                  ) : (
                    <>
                      {confirmRemoval.isLeaving ? (
                        <>
                          <LogOut className="w-4 h-4 mr-2" />
                          Leave Organization
                        </>
                      ) : (
                        <>
                          <Trash2 className="w-4 h-4 mr-2" />
                          Remove {confirmRemoval.isBulk ? 'Members' : 'Member'}
                        </>
                      )}
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
