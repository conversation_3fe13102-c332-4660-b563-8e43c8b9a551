import { useState } from 'react';
import { useAuthStore } from '../../stores/auth-store';
import { Button } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import {
  Building2,
  Users,
  UserPlus,
  Settings,
  Shield,
  CreditCard,
  BarChart3,
  FileText,
  Bell,
  Search,
  X
} from 'lucide-react';
import { InviteToOrganization } from './InviteToOrganization';
import { OrganizationMembers } from './OrganizationMembers';

type MenuSection =
  | 'overview'
  | 'manage-users'
  | 'invite-users'
  | 'organization-settings'
  | 'security'
  | 'billing'
  | 'analytics'
  | 'audit-logs'
  | 'notifications';

const menuItems = [
  {
    id: 'organization-settings' as MenuSection,
    label: 'Organization Settings',
    icon: Settings,
    description: 'Configure organization preferences'
  },
  {
    id: 'manage-users' as MenuSection,
    label: 'Manage Users',
    icon: Users,
    description: 'View and manage organization members'
  },
  {
    id: 'invite-users' as MenuSection,
    label: 'Invite to Organization',
    icon: UserPlus,
    description: 'Invite new members to join'
  },
  {
    id: 'billing' as MenuSection,
    label: 'Billing & Subscription',
    icon: CreditCard,
    description: 'Manage billing and subscription plans'
  },
  {
    id: 'analytics' as MenuSection,
    label: 'Analytics & Reports',
    icon: BarChart3,
    description: 'View usage analytics and reports'
  },
  {
    id: 'audit-logs' as MenuSection,
    label: 'Audit Logs',
    icon: FileText,
    description: 'View organization activity logs'
  },
  {
    id: 'notifications' as MenuSection,
    label: 'Notifications',
    icon: Bell,
    description: 'Configure notification settings'
  }
];

export function OrganizationManagement() {
  const [activeSection, setActiveSection] = useState<MenuSection>('overview');
  const { currentOrganization } = useAuthStore();

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Building2 className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground">No Organization Selected</h3>
          <p className="text-sm text-muted-foreground">Please select an organization to manage.</p>
        </div>
      </div>
    );
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Organization Overview</h2>
              <p className="text-muted-foreground">
                Welcome to {currentOrganization.name}. Here's a quick overview of your organization.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">+2 from last month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">8</div>
                  <p className="text-xs text-muted-foreground">3 completed this month</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2.4 GB</div>
                  <p className="text-xs text-muted-foreground">of 10 GB available</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );

      case 'manage-users':
        return <OrganizationMembers organizationId={currentOrganization.organization_id} />;

      case 'invite-users':
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Invite to Organization</h2>
              <p className="text-muted-foreground">
                Invite new members to join {currentOrganization.name}.
              </p>
            </div>

            <InviteToOrganization organizationId={currentOrganization.organization_id} />
          </div>
        );

      default:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                {menuItems.find(item => item.id === activeSection)?.label}
              </h2>
              <p className="text-muted-foreground">
                {menuItems.find(item => item.id === activeSection)?.description}
              </p>
            </div>

            <Card>
              <CardContent className="py-12">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 text-muted-foreground">
                    {(() => {
                      const IconComponent = menuItems.find(item => item.id === activeSection)?.icon || Building2;
                      return <IconComponent className="w-12 h-12" />;
                    })()}
                  </div>
                  <h3 className="text-lg font-medium mb-2">Coming Soon</h3>
                  <p className="text-muted-foreground">
                    This feature is currently under development.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <div className="flex h-full">
      {/* Sidebar */}
      <div className="w-64 border-r border-border bg-muted/30 hidden md:block">
        <div className="p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            {currentOrganization.logo ? (
              <img
                src={currentOrganization.logo}
                alt={`${currentOrganization.name} logo`}
                className="w-10 h-10 object-cover rounded border border-border"
              />
            ) : (
              <Building2 className="w-10 h-10 text-muted-foreground" />
            )}
            <div>
              <h1 className="font-semibold text-lg">{currentOrganization.name}</h1>
              <p className="text-sm text-muted-foreground capitalize">{currentOrganization.user_role}</p>
            </div>
          </div>
        </div>

        <nav className="p-4">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-md transition-colors ${
                    activeSection === item.id
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <IconComponent className="w-4 h-4 flex-shrink-0" />
                  <span className="text-sm font-medium">{item.label}</span>
                </button>
              );
            })}
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {/* Mobile Header */}
        <div className="md:hidden p-4 border-b border-border bg-muted/30">
          <div className="flex items-center space-x-3">
            {currentOrganization.logo ? (
              <img
                src={currentOrganization.logo}
                alt={`${currentOrganization.name} logo`}
                className="w-8 h-8 object-cover rounded border border-border"
              />
            ) : (
              <Building2 className="w-8 h-8 text-muted-foreground" />
            )}
            <div>
              <h1 className="font-semibold">{currentOrganization.name}</h1>
              <p className="text-xs text-muted-foreground capitalize">{currentOrganization.user_role}</p>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className="md:hidden p-4 border-b border-border">
          <select
            value={activeSection}
            onChange={(e) => setActiveSection(e.target.value as MenuSection)}
            className="w-full p-2 border border-border bg-background text-foreground rounded-md"
          >
            {menuItems.map((item) => (
              <option key={item.id} value={item.id} className="text-foreground bg-background">
                {item.label}
              </option>
            ))}
          </select>
        </div>

        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
