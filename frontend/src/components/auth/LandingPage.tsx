import { useState } from 'react';
import { Button } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { ThemeToggle } from '../ui/ThemeToggle';
import { SignInForm } from './SignInForm';
import { SignUpForm } from './SignUpForm';
import { LayoutDashboard, MessageSquare, Receipt, FileText, Shield, Zap, Users } from 'lucide-react';

type AuthMode = 'landing' | 'signin' | 'signup';

export function LandingPage() {
  const [authMode, setAuthMode] = useState<AuthMode>('landing');

  if (authMode === 'signin') {
    return <SignInForm onBack={() => setAuthMode('landing')} onSwitchToSignUp={() => setAuthMode('signup')} />;
  }

  if (authMode === 'signup') {
    return <SignUpForm onBack={() => setAuthMode('landing')} onSwitchToSignIn={() => setAuthMode('signin')} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <LayoutDashboard className="w-8 h-8 text-primary" />
              <h1 className="text-2xl font-bold text-foreground">DeepLedger</h1>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeToggle />
              <Button variant="ghost" onClick={() => setAuthMode('signin')}>
                Sign In
              </Button>
              <Button onClick={() => setAuthMode('signup')}>
                Get Started
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-5xl font-bold text-foreground mb-6">
            AI-Powered Accounting
            <span className="text-primary block">Made Simple</span>
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            Transform your financial management with DeepLedger's intelligent accounting assistant.
            Record transactions, generate reports, and get insights using natural language.
          </p>
          <div className="space-x-4">
            <Button size="lg" onClick={() => setAuthMode('signup')}>
              Start Free Trial
            </Button>
            <Button variant="outline" size="lg" onClick={() => setAuthMode('signin')}>
              Sign In
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Everything you need for modern accounting
            </h3>
            <p className="text-lg text-muted-foreground">
              Powerful features designed to streamline your financial workflow
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <MessageSquare className="w-12 h-12 text-blue-600 mb-4" />
                <CardTitle>AI Assistant</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Chat with your accounting system using natural language. Record transactions,
                  ask questions, and get insights instantly.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Receipt className="w-12 h-12 text-green-600 mb-4" />
                <CardTitle>Smart Transactions</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Automatically categorize and process transactions with AI-powered
                  recognition and intelligent suggestions.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <FileText className="w-12 h-12 text-purple-600 mb-4" />
                <CardTitle>Instant Reports</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Generate comprehensive financial reports on demand. From P&L statements
                  to cash flow analysis, get what you need instantly.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Shield className="w-12 h-12 text-red-600 mb-4" />
                <CardTitle>Bank-Level Security</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Your financial data is protected with enterprise-grade security,
                  encryption, and compliance standards.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="w-12 h-12 text-yellow-600 mb-4" />
                <CardTitle>Lightning Fast</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Process thousands of transactions in seconds. Our optimized
                  infrastructure ensures your accounting never slows you down.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Users className="w-12 h-12 text-indigo-600 mb-4" />
                <CardTitle>Team Collaboration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Work together with your team, accountant, and stakeholders.
                  Share insights and collaborate in real-time.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Card className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0">
            <CardContent className="py-12">
              <h3 className="text-3xl font-bold mb-4">
                Ready to revolutionize your accounting?
              </h3>
              <p className="text-xl mb-8 text-blue-100">
                Join thousands of businesses already using DeepLedger to streamline their finances.
              </p>
              <Button
                size="lg"
                variant="secondary"
                onClick={() => setAuthMode('signup')}
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                Get Started Today
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <LayoutDashboard className="w-6 h-6" />
              <span className="text-lg font-semibold">DeepLedger</span>
            </div>
            <p className="text-gray-400">
              © 2024 DeepLedger. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
