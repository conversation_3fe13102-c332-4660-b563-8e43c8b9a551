import React, { useEffect } from 'react';
import { MessageSquare, Plus, Trash2, Loader2, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { useChatStore } from '../../stores/chat-store';
import { useAuthStore } from '../../stores/auth-store';
import { cn } from '../../utils/cn';

interface ChatHistoryProps {
  className?: string;
}

export function ChatHistory({ className }: ChatHistoryProps) {
  const { user } = useAuthStore();
  const {
    threads,
    isLoadingThreads,
    error, // Simplified error handling
    currentThreadId,
    loadThreads,
    createNewThread,
    switchToThread,
    deleteThread,
    setError, // Simplified error setter
  } = useChatStore();

  // Removed edit functionality for simplicity

  // Load threads when user is available
  useEffect(() => {
    if (user?.id) {
      loadThreads(user.id).catch((error) => {
        console.error('Failed to load threads:', error);
      });
    }
  }, [user?.id, loadThreads]);

  const handleCreateNewThread = async () => {
    if (!user?.id) return;

    try {
      const newThread = await createNewThread(user.id, 'New Conversation');
      await switchToThread(newThread.id);
    } catch (error) {
      console.error('Failed to create new thread:', error);
    }
  };

  const handleSwitchThread = async (threadId: string) => {
    if (threadId === currentThreadId) return;
    await switchToThread(threadId);
  };

  const handleDeleteThread = async (threadId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      try {
        await deleteThread(threadId);
      } catch (error) {
        console.error('Failed to delete thread:', error);
        // Error is already handled in the store, but we can add additional feedback here if needed
      }
    }
  };

  // Removed edit functions for simplicity

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const truncateTitle = (title: string, maxLength: number = 40) => {
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  return (
    <div className={cn('w-80 bg-card border-l border-border flex flex-col', className)}>
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-muted-foreground" />
            <h2 className="text-sm font-semibold text-foreground">Chat History</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCreateNewThread}
            disabled={!user?.id}
            className="text-muted-foreground hover:text-foreground hover:bg-accent p-2"
            title="New conversation"
          >
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-destructive/10 border-b border-destructive/20 text-destructive text-sm flex items-center space-x-2">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <span className="flex-1">{error}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setError(null)}
            className="text-destructive hover:text-destructive p-1"
          >
            ×
          </Button>
        </div>
      )}

      {/* Loading State */}
      {isLoadingThreads && (
        <div className="p-4 flex items-center justify-center text-muted-foreground">
          <Loader2 className="w-4 h-4 animate-spin mr-2" />
          <span className="text-sm">Loading conversations...</span>
        </div>
      )}

      {/* Threads List */}
      <div className="flex-1 overflow-y-auto">
        {!isLoadingThreads && threads.length === 0 && (
          <div className="p-4 text-center text-muted-foreground">
            <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs mt-1">Start a new conversation to see it here</p>
          </div>
        )}

        {threads.map((thread) => (
          <div
            key={thread.id}
            onClick={() => handleSwitchThread(thread.id)}
            className={cn(
              'p-3 border-b border-border/50 cursor-pointer transition-colors hover:bg-accent/50 group',
              currentThreadId === thread.id && 'bg-accent/70 border-l-2 border-l-primary'
            )}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-medium text-foreground truncate">
                  {truncateTitle(thread.title || 'Untitled Conversation')}
                </h3>
                <p className="text-xs text-muted-foreground mt-1">
                  {formatDate(thread.updatedAt)}
                </p>
              </div>

              {/* Simplified - only delete button */}
              <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => handleDeleteThread(thread.id, e)}
                  className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  title="Delete conversation"
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
