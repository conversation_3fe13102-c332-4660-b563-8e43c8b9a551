import React, { useRef, useEffect } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '../../utils/cn';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyPress?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
  className?: string;
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onKeyPress,
  disabled = false,
  isLoading = false,
  placeholder = "Message DeepLedger AI...",
  className
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!disabled && !isLoading && value.trim()) {
        onSend();
      }
    }
    onKeyPress?.(e);
  };

  return (
    <div className={cn("relative", className)}>
      {/* Main Input Container */}
      <div className="relative flex items-end bg-background border border-input rounded-3xl shadow-sm hover:shadow-md transition-all duration-200 focus-within:border-ring focus-within:ring-1 focus-within:ring-ring focus-within:shadow-lg">

        {/* Text Input */}
        <div className="flex-1 min-h-[3.5rem] max-h-[200px] overflow-hidden">
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className="w-full resize-none border-0 bg-transparent px-6 py-4 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-0 disabled:cursor-not-allowed disabled:opacity-50"
            style={{
              minHeight: '3.5rem',
              lineHeight: '1.6',
            }}
          />
        </div>

        {/* Send Button */}
        <div className="flex-shrink-0 p-1.5">
          <Button
            onClick={onSend}
            disabled={!value.trim() || isLoading || disabled}
            size="icon"
            className={cn(
              "h-9 w-9 rounded-full transition-all duration-200 border-0",
              value.trim() && !isLoading && !disabled
                ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md"
                : "bg-muted hover:bg-muted text-muted-foreground cursor-not-allowed"
            )}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Character count or other info */}
      {value.length > 0 && (
        <div className="absolute -bottom-6 right-0 text-xs text-muted-foreground">
          {value.length} characters
        </div>
      )}
    </div>
  );
}
