import { useState } from 'react';
import { ChevronDown, ChevronRight, Wrench, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import type { ToolCallDisplay as ToolCallType } from '../../types/chat';
import { Button } from '../ui/Button';

interface ToolCallDisplayProps {
  toolCall: ToolCallType;
}

export function ToolCallDisplay({ toolCall }: ToolCallDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    switch (toolCall.status) {
      case 'running':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Wrench className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (toolCall.status) {
      case 'running':
        return 'Running...';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="bg-card border border-border rounded-lg border-l-4 border-l-primary">
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <Wrench className="w-4 h-4 text-primary" />
            <span className="text-foreground font-medium">{toolCall.name}</span>
            {getStatusIcon()}
            <span className="text-xs text-muted-foreground">
              {getStatusText()}
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground hover:bg-accent"
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3" />
            ) : (
              <ChevronRight className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-3 space-y-3">
          {/* Arguments */}
          {Object.keys(toolCall.args).length > 0 && (
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">
                Arguments:
              </h4>
              <div className="bg-muted border border-border rounded-md p-2 text-xs font-mono text-muted-foreground">
                <pre className="whitespace-pre-wrap">
                  {JSON.stringify(toolCall.args, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Result */}
          {toolCall.result && (
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">
                Result:
              </h4>
              <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-md p-2 text-xs text-green-700 dark:text-green-300">
                <pre className="whitespace-pre-wrap">
                  {typeof toolCall.result === 'string'
                    ? toolCall.result
                    : JSON.stringify(toolCall.result, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Error */}
          {toolCall.error && (
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">
                Error:
              </h4>
              <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-md p-2 text-xs text-red-700 dark:text-red-300">
                {toolCall.error}
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div className="text-xs text-muted-foreground">
            {toolCall.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            })}
          </div>
        </div>
      )}
    </div>
  );
}
