import React, { useState, useRef, useEffect } from 'react';
import { Loader2, Bot } from 'lucide-react';
import { Button } from '../ui/Button';
import { MessageBubble } from './MessageBubble';
import { ToolCallDisplay } from './ToolCallDisplay';
import { ChatInput } from './ChatInput';
import { FinancialMetricsSidebar } from '../ui/FinancialMetricsSidebar';
import { ChatHistory } from './ChatHistory';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { useChatStore } from '../../stores/chat-store';
import { useAppStore } from '../../stores/app-store';
import { useAuthStore } from '../../stores/auth-store';
import { useFinancialMetricsStore } from '../../stores/financial-metrics-store';
import { tokenManager } from '../../services/api-client';
import mastraClient from '../../services/mastra-client';





export function ChatInterface() {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { user } = useAuthStore();
  const {
    messages,
    currentStreamingMessage,
    toolCalls,
    isAgentTyping,
    isConnected,
    error, // Simplified error handling
    currentThreadId,
    addMessage,
    setCurrentThread,
    getCurrentThread,
    createNewThread,
  } = useChatStore();

  const { isLoading, setError } = useAppStore();
  const { updateMetricsFromTransaction } = useFinancialMetricsStore();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, currentStreamingMessage]);

  // Initialize authentication and thread management
  useEffect(() => {
    // Set up authentication token
    const accessToken = tokenManager.getAccessToken();
    if (accessToken) {
      mastraClient.setAuthToken(accessToken);
    }

    // Initialize thread if not exists
    if (!currentThreadId && user?.id) {
      // Create a new thread using the memory API
      createNewThread(user.id, 'New Conversation').then((thread) => {
        setCurrentThread(thread.id, user.id);
      }).catch((error) => {
        console.error('Failed to create initial thread:', error);
        // Fallback to the old method
        const threadId = `thread_${user.id}_${Date.now()}`;
        setCurrentThread(threadId, user.id);
      });
    }
  }, [user, currentThreadId, setCurrentThread]);

  // Test connection to backend
  useEffect(() => {
    const testConnection = async () => {
      try {
        const isConnected = await mastraClient.testConnection();
        console.log('Mastra backend connection:', isConnected ? 'Connected' : 'Disconnected');
      } catch (error) {
        console.error('Failed to test connection:', error);
      }
    };

    testConnection();
  }, []);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !user) return;

    const userMessage = {
      id: `user_${Date.now()}`,
      role: 'user' as const,
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    // Add user message to store
    addMessage(userMessage);

    // Clear input
    setInputValue('');

    try {
      // Get current thread info
      const { threadId, resourceId } = getCurrentThread();

      if (!threadId || !resourceId) {
        setError('No active conversation thread. Please refresh the page.');
        return;
      }

      // Send message to Mastra client with streaming support
      await mastraClient.sendMessageStream(
        userMessage.content,
        {
          threadId,
          resourceId,
        },
        // onChunk - handle streaming text
        (chunk: string) => {
          // Handle streaming message chunks
          console.log('Received chunk:', chunk);
          // You can update the streaming message in the store here
        },
        // onToolCall - handle tool calls
        (toolCall: any) => {
          console.log('Tool call:', toolCall);
          // Handle tool calls here
        },
        // onComplete - handle completion
        () => {
          console.log('Message stream completed');
        },
        // onError - handle errors
        (error: Error) => {
          console.error('Error sending message:', error);
          setError('Failed to send message. Please try again.');
        }
      );
    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Demo function to simulate transaction updates
  const simulateTransaction = (type: 'income' | 'expense', amount: number) => {
    updateMetricsFromTransaction(amount, type);
    console.log(`Simulated ${type} transaction: $${amount}`);
  };

  // Listen for transaction-related messages to update metrics
  useEffect(() => {
    // This is a simple demo - in real implementation, this would be triggered by actual transaction events
    const messageText = inputValue.toLowerCase();
    if (messageText.includes('revenue') || messageText.includes('income') || messageText.includes('sale')) {
      // Simulate income transaction
      const amount = Math.floor(Math.random() * 5000) + 1000; // Random amount between 1000-6000
      setTimeout(() => simulateTransaction('income', amount), 1000);
    } else if (messageText.includes('expense') || messageText.includes('cost') || messageText.includes('bill')) {
      // Simulate expense transaction
      const amount = Math.floor(Math.random() * 3000) + 500; // Random amount between 500-3500
      setTimeout(() => simulateTransaction('expense', amount), 1000);
    }
  }, [messages.length]); // Trigger when new messages are added

  return (
    <div className="flex h-full bg-background text-foreground">
      {/* Left Sidebar - Financial Metrics */}
      <ErrorBoundary>
        <FinancialMetricsSidebar />
      </ErrorBoundary>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Error Display */}
        {error && (
          <div className="p-3 bg-destructive/10 border-b border-destructive/20 text-destructive text-sm">
            {error}
          </div>
        )}

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6 bg-background">
          {messages.length === 0 && (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <Bot className="w-16 h-16 text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">
                Welcome to DeepLedger AI
              </h3>
              <p className="text-muted-foreground max-w-md">
                I'm here to help you with your accounting needs. You can ask me to:
              </p>
              <div className="mt-4 space-y-2 text-sm text-muted-foreground">
                <div>• Recording financial transactions and payments</div>
                <div>• Managing your chart of accounts</div>
                <div>• Tracking expenses, payments, and items</div>
                <div>• Generating financial reports and insights</div>
              </div>
              <p className="text-muted-foreground/80 text-sm mt-4">
                Get started by asking me about your organization. Could you please tell me:
              </p>
              <div className="mt-2 space-y-1 text-sm text-muted-foreground/80">
                <div>1. Your organization's name</div>
                <div>2. Whether you use cash or accrual accounting</div>
                <div>3. Your fiscal year calendar year</div>
              </div>

              {/* Demo Transaction Buttons */}
              <div className="mt-6 p-4 bg-muted/30 rounded-lg border border-border">
                <p className="text-sm font-medium text-foreground mb-3">Demo: Record Transactions</p>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => simulateTransaction('income', 2500)}
                    className="text-green-600 hover:bg-green-50 dark:hover:bg-green-950"
                  >
                    + Add Revenue ($2,500)
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => simulateTransaction('expense', 1200)}
                    className="text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
                  >
                    - Add Expense ($1,200)
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => simulateTransaction('income', 5000)}
                    className="text-green-600 hover:bg-green-50 dark:hover:bg-green-950"
                  >
                    + Large Sale ($5,000)
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Click these buttons to see the financial metrics update in real-time!
                </p>
              </div>
            </div>
          )}

          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}

          {/* Current streaming message */}
          {currentStreamingMessage && (
            <MessageBubble message={currentStreamingMessage} />
          )}

          {/* Tool calls */}
          {toolCalls.map((toolCall) => (
            <ToolCallDisplay key={toolCall.id} toolCall={toolCall} />
          ))}

          {/* Typing indicator */}
          {isAgentTyping && !currentStreamingMessage && (
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-sm">DeepLedger is thinking...</span>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Fixed Input Area at Bottom */}
        <div className="p-6 bg-background">
          <div className="max-w-4xl mx-auto">
            <ChatInput
              value={inputValue}
              onChange={setInputValue}
              onSend={handleSendMessage}
              onKeyPress={handleKeyPress}
              disabled={!isConnected}
              isLoading={isLoading}
              placeholder="Message DeepLedger AI..."
            />
          </div>
        </div>
      </div>

      {/* Right Sidebar - Chat History */}
      <ErrorBoundary>
        <ChatHistory />
      </ErrorBoundary>
    </div>
  );
}
