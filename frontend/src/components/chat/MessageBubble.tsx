import { Use<PERSON>, <PERSON><PERSON>, <PERSON>ader2 } from 'lucide-react';
import type { ChatMessage } from '../../types/chat';
import { cn } from '../../utils/cn';

interface MessageBubbleProps {
  message: ChatMessage;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user';
  const isStreaming = message.isStreaming;

  return (
    <div
      className={cn(
        'flex items-start space-x-4',
        isUser ? 'flex-row-reverse space-x-reverse' : 'flex-row'
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
          isUser
            ? 'bg-primary text-primary-foreground'
            : 'bg-muted text-primary border border-border'
        )}
      >
        {isUser ? (
          <User className="w-5 h-5" />
        ) : (
          <Bot className="w-5 h-5" />
        )}
      </div>

      {/* Message Content */}
      <div
        className={cn(
          'flex-1 max-w-[75%]',
          isUser ? 'text-right' : 'text-left'
        )}
      >
        {/* Role Label */}
        <div
          className={cn(
            'text-xs font-medium mb-1',
            isUser ? 'text-primary' : 'text-muted-foreground'
          )}
        >
          {isUser ? 'You' : 'DeepLedger AI'}
        </div>

        {/* Message Bubble */}
        <div
          className={cn(
            'inline-block px-4 py-3 rounded-2xl text-sm leading-relaxed',
            isUser
              ? 'bg-primary text-primary-foreground rounded-br-md'
              : 'bg-card text-card-foreground border border-border rounded-bl-md'
          )}
        >
          <div className="whitespace-pre-wrap break-words">
            {message.content}
            {isStreaming && (
              <span className="inline-flex items-center ml-2">
                <Loader2 className="w-3 h-3 animate-spin" />
              </span>
            )}
          </div>
        </div>

        {/* Timestamp */}
        <div
          className={cn(
            'text-xs text-muted-foreground mt-1',
            isUser ? 'text-right' : 'text-left'
          )}
        >
          {message.timestamp.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })}
        </div>
      </div>
    </div>
  );
}
