import { create } from 'zustand';
import type { ChatMessage, ToolCallDisplay } from '../types/chat';
import type { Thread } from '../services/memory-api';
import { memoryApi } from '../services/memory-api';

// Simplified chat state - removed complex state management
interface ChatState {
  messages: ChatMessage[];
  currentStreamingMessage: ChatMessage | null;
  toolCalls: ToolCallDisplay[];
  isAgentTyping: boolean;
  isConnected: boolean;
  error: string | null; // Simplified error handling
  currentThreadId: string | null;
  currentResourceId: string | null;

  // Simplified thread management
  threads: Thread[];
  isLoadingThreads: boolean;
}

interface ChatActions {
  // Message management
  addMessage: (message: ChatMessage) => void;
  setMessages: (messages: ChatMessage[]) => void;
  clearMessages: () => void;

  // Streaming message handling
  startStreamingMessage: (message: ChatMessage) => void;
  updateStreamingMessage: (content: string) => void;
  finishStreamingMessage: () => void;

  // Tool call management
  startToolCall: (toolCall: ToolCallDisplay) => void;
  updateToolCallArgs: (toolCallId: string, args: Record<string, any>) => void;
  finishToolCall: (toolCallId: string, result?: any, error?: string) => void;
  clearToolCalls: () => void;

  // Agent state
  setAgentTyping: (typing: boolean) => void;

  // Connection state
  setConnected: (connected: boolean) => void;
  setError: (error: string | null) => void;

  // Thread management
  setCurrentThread: (threadId: string, resourceId: string) => void;
  getCurrentThread: () => { threadId: string | null; resourceId: string | null };
  clearCurrentThread: () => void;

  // Simplified thread operations
  loadThreads: (resourceId: string) => Promise<void>;
  createNewThread: (resourceId: string, title?: string) => Promise<Thread>;
  switchToThread: (threadId: string) => Promise<void>;
  deleteThread: (threadId: string) => Promise<void>;
}

type ChatStore = ChatState & ChatActions;

export const useChatStore = create<ChatStore>((set, get) => ({
  // Simplified initial state
  messages: [],
  currentStreamingMessage: null,
  toolCalls: [],
  isAgentTyping: false,
  isConnected: false,
  error: null, // Single error field
  currentThreadId: null,
  currentResourceId: null,

  // Thread state
  threads: [],
  isLoadingThreads: false,

  // Message management
  addMessage: (message) =>
    set((state) => ({
      messages: [...state.messages, message],
    })),

  setMessages: (messages) =>
    set({ messages }),

  clearMessages: () =>
    set({ messages: [], toolCalls: [] }),

  // Streaming message handling
  startStreamingMessage: (message) =>
    set({ currentStreamingMessage: message }),

  updateStreamingMessage: (content) =>
    set((state) => {
      if (!state.currentStreamingMessage) return state;

      return {
        currentStreamingMessage: {
          ...state.currentStreamingMessage,
          content: state.currentStreamingMessage.content + content,
        },
      };
    }),

  finishStreamingMessage: () =>
    set((state) => {
      if (!state.currentStreamingMessage) return state;

      const finishedMessage = {
        ...state.currentStreamingMessage,
        isStreaming: false,
      };

      return {
        messages: [...state.messages, finishedMessage],
        currentStreamingMessage: null,
      };
    }),

  // Tool call management
  startToolCall: (toolCall) =>
    set((state) => ({
      toolCalls: [...state.toolCalls, toolCall],
    })),

  updateToolCallArgs: (toolCallId, args) =>
    set((state) => ({
      toolCalls: state.toolCalls.map((toolCall) =>
        toolCall.id === toolCallId
          ? { ...toolCall, args: { ...toolCall.args, ...args } }
          : toolCall
      ),
    })),

  finishToolCall: (toolCallId, result, error) =>
    set((state) => ({
      toolCalls: state.toolCalls.map((toolCall) =>
        toolCall.id === toolCallId
          ? {
              ...toolCall,
              status: error ? 'error' : 'completed',
              result,
              error,
            }
          : toolCall
      ),
    })),

  clearToolCalls: () =>
    set({ toolCalls: [] }),

  // Agent state
  setAgentTyping: (typing) =>
    set({ isAgentTyping: typing }),

  // Simplified connection state
  setConnected: (connected) =>
    set({ isConnected: connected, error: connected ? null : get().error }),

  setError: (error) =>
    set({ error, isConnected: error ? false : get().isConnected }),

  // Thread management
  setCurrentThread: (threadId, resourceId) =>
    set({ currentThreadId: threadId, currentResourceId: resourceId }),

  getCurrentThread: () => {
    const state = get();
    return { threadId: state.currentThreadId, resourceId: state.currentResourceId };
  },

  clearCurrentThread: () =>
    set({ currentThreadId: null, currentResourceId: null }),

  // Simplified thread management
  loadThreads: async (resourceId: string) => {
    set({ isLoadingThreads: true, error: null });
    try {
      const threads = await memoryApi.getThreads(resourceId);
      set({ threads, isLoadingThreads: false });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load threads';
      set({ error: errorMessage, isLoadingThreads: false });
    }
  },

  createNewThread: async (resourceId: string, title?: string) => {
    try {
      const thread = await memoryApi.createThread({ resourceId, title });
      set((state) => ({ threads: [thread, ...state.threads] }));
      return thread;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create thread';
      set({ error: errorMessage });
      throw error;
    }
  },

  switchToThread: async (threadId: string) => {
    const state = get();
    if (!state.currentResourceId) return;

    try {
      // Load recent messages (reduced from 50 to 20 for better performance)
      const { uiMessages } = await memoryApi.getMessages({
        threadId,
        resourceId: state.currentResourceId,
        selectBy: { last: 20 }
      });

      // Convert to ChatMessage format, filtering out system messages
      const chatMessages: ChatMessage[] = uiMessages
        .filter(msg => msg.role === 'user' || msg.role === 'assistant')
        .map(msg => ({
          id: msg.id,
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
          timestamp: new Date(msg.timestamp),
        }));

      set({
        currentThreadId: threadId,
        messages: chatMessages,
        currentStreamingMessage: null,
        toolCalls: [],
        error: null // Clear any previous errors
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to switch thread';
      set({ error: errorMessage });
    }
  },

  deleteThread: async (threadId: string) => {
    try {
      await memoryApi.deleteThread(threadId);

      set((state) => ({
        threads: state.threads.filter(t => t.id !== threadId),
        // If deleting current thread, clear it
        currentThreadId: state.currentThreadId === threadId ? null : state.currentThreadId,
        messages: state.currentThreadId === threadId ? [] : state.messages,
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete thread';
      set({ error: errorMessage });
    }
  },
}));
