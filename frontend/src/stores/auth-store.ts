import { create } from 'zustand';
import { authService } from '../services/auth-service';
import { tokenManager } from '../services/api-client';
import { organizationAPI, type OrganizationWithRole } from '../services/organization-api';
import type { User, AuthError } from '../services/auth-service';

interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
  organizations: OrganizationWithRole[];
  currentOrganization: OrganizationWithRole | null;
  organizationsLoading: boolean;
}

interface AuthActions {
  signIn: (email: string, password: string) => Promise<{ error?: AuthError }>;
  signUp: (email: string, password: string, userData?: { full_name?: string }) => Promise<{ error?: AuthError }>;
  signOut: () => Promise<{ error?: AuthError }>;
  updateProfile: (updates: { full_name?: string; avatar_url?: string }) => Promise<{ error?: AuthError }>;
  resetPassword: (email: string) => Promise<{ error?: AuthError }>;
  initialize: () => Promise<void>;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  // Organization actions
  fetchOrganizations: () => Promise<void>;
  switchOrganization: (organization: OrganizationWithRole) => void;
  createOrganization: (data: { name: string; logo?: string }) => Promise<{ error?: string }>;
  refreshOrganizations: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>((set, get) => ({
  // Initial state
  user: null,
  loading: true,
  initialized: false,
  organizations: [],
  currentOrganization: null,
  organizationsLoading: false,

  // Actions
  signIn: async (email: string, password: string) => {
    set({ loading: true });
    try {
      console.log('🔐 Starting sign in process...');
      const { data, error } = await authService.signIn({ email, password });

      if (error) {
        console.error('❌ Sign in error:', error);
        set({ loading: false });
        return { error };
      }

      if (data) {
        console.log('✅ Sign in successful, user data:', data.user);
        set({ user: data.user, loading: false });
        // Fetch organizations after successful sign in
        get().fetchOrganizations();
      }

      return {};
    } catch (error) {
      console.error('❌ Sign in exception:', error);
      set({ loading: false });
      return { error: { message: 'Network error during sign in' } };
    }
  },

  signUp: async (email: string, password: string, userData?: { full_name?: string }) => {
    set({ loading: true });
    try {
      const { data, error } = await authService.signUp({
        email,
        password,
        full_name: userData?.full_name
      });
      if (error) {
        set({ loading: false });
        return { error };
      }
      if (data) {
        set({ user: data.user, loading: false });
        // Fetch organizations after successful sign up
        get().fetchOrganizations();
      }
      return {};
    } catch (error) {
      set({ loading: false });
      return { error: { message: 'Network error during sign up' } };
    }
  },

  signOut: async () => {
    set({ loading: true });
    try {
      const { error } = await authService.signOut();
      set({
        user: null,
        loading: false,
        organizations: [],
        currentOrganization: null
      });
      return { error };
    } catch (error) {
      console.error('Sign out error:', error);
      set({
        user: null,
        loading: false,
        organizations: [],
        currentOrganization: null
      });
      return { error: { message: 'Network error during sign out' } };
    }
  },

  updateProfile: async (updates: { full_name?: string; avatar_url?: string }) => {
    set({ loading: true });
    try {
      const { data, error } = await authService.updateProfile(updates);
      if (error) {
        set({ loading: false });
        return { error };
      }
      // Update local user state
      if (data) {
        set({ user: data, loading: false });
      }
      return {};
    } catch (error) {
      set({ loading: false });
      return { error: { message: 'Network error during profile update' } };
    }
  },

  resetPassword: async (email: string) => {
    try {
      const { error } = await authService.resetPassword({ email });
      return { error };
    } catch (error) {
      return { error: { message: 'Network error during password reset' } };
    }
  },

  initialize: async () => {
    try {
      // Check if user has valid token and get user info
      if (tokenManager.hasValidToken()) {
        const { user, error } = await authService.getUser();
        if (user && !error) {
          set({
            user,
            loading: false,
            initialized: true
          });
          // Fetch organizations after successful initialization
          get().fetchOrganizations();
        } else {
          // Token might be invalid, clear it
          tokenManager.clearTokens();
          set({
            user: null,
            loading: false,
            initialized: true
          });
        }
      } else {
        set({
          user: null,
          loading: false,
          initialized: true
        });
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      set({ user: null, loading: false, initialized: true });
    }
  },

  setUser: (user: User | null) => set({ user }),
  setLoading: (loading: boolean) => set({ loading }),

  // Organization actions
  fetchOrganizations: async () => {
    const { user } = get();
    if (!user) {
      return;
    }

    set({ organizationsLoading: true });
    try {
      const organizations = await organizationAPI.getUserOrganizations();
      const currentOrg = organizations.length > 0 ? organizations[0] : null;

      set({
        organizations,
        currentOrganization: currentOrg,
        organizationsLoading: false
      });
    } catch (error) {
      console.error('Failed to fetch organizations:', error);
      set({ organizationsLoading: false });
    }
  },

  switchOrganization: (organization: OrganizationWithRole) => {
    set({ currentOrganization: organization });
  },

  createOrganization: async (data: { name: string; logo?: string }) => {
    try {
      const newOrganization = await organizationAPI.createOrganization(data);

      // Add to organizations list and set as current
      const { organizations } = get();
      const updatedOrganizations = [...organizations, { ...newOrganization, user_role: 'owner' }];

      set({
        organizations: updatedOrganizations,
        currentOrganization: { ...newOrganization, user_role: 'owner' }
      });

      return {};
    } catch (error) {
      console.error('Failed to create organization:', error);
      return { error: error instanceof Error ? error.message : 'Failed to create organization' };
    }
  },

  refreshOrganizations: async () => {
    await get().fetchOrganizations();
  },
}));
