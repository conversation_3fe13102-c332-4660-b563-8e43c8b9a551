import { create } from 'zustand';
import type { FinancialMetrics, FinancialMetricsState, TimePeriod } from '../types/financial-metrics-simple';
import { TIME_PERIODS } from '../types/financial-metrics-simple';

interface FinancialMetricsActions {
  setSelectedPeriod: (period: TimePeriod) => void;
  setMetrics: (metrics: FinancialMetrics) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCollapsed: (collapsed: boolean) => void;
  fetchMetrics: (period: TimePeriod) => Promise<void>;
  updateMetricsFromTransaction: (transactionAmount: number, transactionType: 'income' | 'expense') => void;
}

type FinancialMetricsStore = FinancialMetricsState & FinancialMetricsActions;

// Mock data generator for financial metrics
const generateMockMetrics = (period: TimePeriod): FinancialMetrics => {
  // Generate realistic mock data based on the period
  const baseRevenue = 50000;
  const baseCosts = 35000;
  const baseAssets = 150000;
  const baseLiabilities = 75000;

  // Add some randomness to make it feel more realistic
  const randomFactor = 0.8 + Math.random() * 0.4; // 0.8 to 1.2

  const totalRevenue = Math.round(baseRevenue * randomFactor);
  const totalCosts = Math.round(baseCosts * randomFactor);
  const netIncome = totalRevenue - totalCosts;
  const totalAssets = Math.round(baseAssets * randomFactor);
  const totalLiabilities = Math.round(baseLiabilities * randomFactor);
  const ownersEquity = totalAssets - totalLiabilities;

  return {
    netIncome,
    totalRevenue,
    totalCosts,
    totalAssets,
    totalLiabilities,
    ownersEquity,
    period,
    lastUpdated: new Date(),
  };
};

export const useFinancialMetricsStore = create<FinancialMetricsStore>((set, get) => ({
  // Initial state
  metrics: null,
  selectedPeriod: TIME_PERIODS[0], // Default to "This Month"
  isLoading: false,
  error: null,
  isCollapsed: false,

  // Actions
  setSelectedPeriod: (period: TimePeriod) => {
    set({ selectedPeriod: period });
    // Automatically fetch metrics for the new period
    get().fetchMetrics(period);
  },

  setMetrics: (metrics: FinancialMetrics) => {
    set({ metrics, error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error, isLoading: false });
  },

  setCollapsed: (collapsed: boolean) => {
    set({ isCollapsed: collapsed });
  },

  fetchMetrics: async (period: TimePeriod) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate mock data for now
      const mockMetrics = generateMockMetrics(period);

      set({
        metrics: mockMetrics,
        isLoading: false,
        error: null
      });
    } catch (error) {
      set({
        error: 'Failed to fetch financial metrics',
        isLoading: false
      });
    }
  },

  updateMetricsFromTransaction: (transactionAmount: number, transactionType: 'income' | 'expense') => {
    const currentMetrics = get().metrics;
    if (!currentMetrics) return;

    const updatedMetrics = { ...currentMetrics };

    if (transactionType === 'income') {
      updatedMetrics.totalRevenue += transactionAmount;
      updatedMetrics.netIncome += transactionAmount;
      updatedMetrics.totalAssets += transactionAmount;
      updatedMetrics.ownersEquity += transactionAmount;
    } else if (transactionType === 'expense') {
      updatedMetrics.totalCosts += transactionAmount;
      updatedMetrics.netIncome -= transactionAmount;
      updatedMetrics.totalAssets -= transactionAmount;
      updatedMetrics.ownersEquity -= transactionAmount;
    }

    updatedMetrics.lastUpdated = new Date();

    // Add a brief loading state to show the update animation
    set({ isLoading: true });
    setTimeout(() => {
      set({ metrics: updatedMetrics, isLoading: false });
    }, 300);
  },
}));

// Initialize the store with default period metrics
setTimeout(() => {
  const store = useFinancialMetricsStore.getState();
  store.fetchMetrics(store.selectedPeriod);
}, 100);
