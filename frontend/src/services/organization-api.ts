import { api } from './api-client';

// Organization types
export interface Organization {
  organization_id: string;
  name: string;
  slug: string;
  logo?: string | null;
  settings?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: string | null;
  updated_by?: string | null;
}

export interface OrganizationWithRole extends Organization {
  user_role: string;
}

export interface OrganizationUser {
  organization_user_id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  created_at: string;
  updated_at: string;
}

export interface OrganizationMember {
  organization_user_id: string;
  user_id: string;
  role: string;
  created_at: string;
  updated_at: string;
  user: {
    email: string;
    full_name?: string | null;
    avatar_url?: string | null;
  };
}

export interface CreateOrganizationRequest {
  name: string;
  logo?: string;
  settings?: Record<string, any>;
}

export interface UpdateOrganizationRequest {
  name?: string;
  slug?: string;
  logo?: string;
  settings?: Record<string, any>;
}

export interface AddOrganizationUserRequest {
  user_id: string;
  role: 'admin' | 'member' | 'viewer';
}

export interface UpdateOrganizationUserRequest {
  role: 'admin' | 'member' | 'viewer';
}

export interface OrganizationMemberStats {
  total_members: number;
  role_breakdown: Record<string, number>;
  recent_joins: number;
}

export class OrganizationAPI {
  /**
   * Create a new organization
   */
  async createOrganization(data: CreateOrganizationRequest): Promise<Organization> {
    const response = await api.post<Organization>('/api/organizations', data);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create organization');
    }
    return response.data;
  }

  /**
   * Get current user's organizations
   */
  async getUserOrganizations(): Promise<OrganizationWithRole[]> {
    const response = await api.get<OrganizationWithRole[]>('/api/organizations/my');
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch organizations');
    }
    return response.data;
  }

  /**
   * Get organization by ID
   */
  async getOrganization(organizationId: string): Promise<Organization> {
    const response = await api.get<Organization>(`/api/organizations/${organizationId}`);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch organization');
    }
    return response.data;
  }

  /**
   * Update organization
   */
  async updateOrganization(
    organizationId: string,
    data: UpdateOrganizationRequest
  ): Promise<Organization> {
    const response = await api.put<Organization>(`/api/organizations/${organizationId}`, data);
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update organization');
    }
    return response.data;
  }

  /**
   * Delete organization
   */
  async deleteOrganization(organizationId: string): Promise<void> {
    const response = await api.delete(`/api/organizations/${organizationId}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete organization');
    }
  }

  /**
   * Get organization members
   */
  async getOrganizationMembers(organizationId: string): Promise<OrganizationMember[]> {
    const response = await api.get<OrganizationMember[]>(
      `/api/organizations/${organizationId}/members`
    );
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch organization members');
    }
    return response.data;
  }

  /**
   * Get organization member statistics
   */
  async getOrganizationMemberStats(organizationId: string): Promise<OrganizationMemberStats> {
    const response = await api.get<OrganizationMemberStats>(
      `/api/organizations/${organizationId}/members/stats`
    );
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch organization member stats');
    }
    return response.data;
  }

  /**
   * Add user to organization
   */
  async addUserToOrganization(
    organizationId: string,
    data: AddOrganizationUserRequest
  ): Promise<OrganizationUser> {
    const response = await api.post<OrganizationUser>(
      `/api/organizations/${organizationId}/members`,
      data
    );
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to add user to organization');
    }
    return response.data;
  }

  /**
   * Update user role in organization
   */
  async updateUserRole(
    organizationId: string,
    userId: string,
    data: UpdateOrganizationUserRequest
  ): Promise<OrganizationUser> {
    const response = await api.put<OrganizationUser>(
      `/api/organizations/${organizationId}/members/${userId}`,
      data
    );
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update user role');
    }
    return response.data;
  }

  /**
   * Remove user from organization
   */
  async removeUserFromOrganization(organizationId: string, userId: string): Promise<void> {
    const response = await api.delete(`/api/organizations/${organizationId}/members/${userId}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to remove user from organization');
    }
  }

  /**
   * Leave organization
   */
  async leaveOrganization(organizationId: string): Promise<void> {
    const response = await api.post(`/api/organizations/${organizationId}/leave`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to leave organization');
    }
  }
}

// Export singleton instance
export const organizationAPI = new OrganizationAPI();
