import { api, token<PERSON>anager } from './api-client';
import type { AuthTokens, ApiResponse } from './api-client';

// Types for auth requests and responses
export interface SignUpRequest {
  email: string;
  password: string;
  full_name?: string;
}

export interface SignInRequest {
  email: string;
  password: string;
}

export interface ResetPasswordRequest {
  email: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface UpdateProfileRequest {
  full_name?: string;
  avatar_url?: string;
}

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface AuthError {
  message: string;
  code?: string;
}

// Auth service class
export class AuthService {
  /**
   * Sign up a new user
   */
  async signUp(request: SignUpRequest): Promise<{ data?: AuthResponse; error?: AuthError }> {
    try {
      const response = await api.post<AuthResponse>('/api/auth/register', request);

      if (response.success && response.data) {
        // Store tokens
        tokenManager.setTokens({
          access_token: response.data.access_token,
          refresh_token: response.data.refresh_token,
          expires_in: response.data.expires_in,
          token_type: 'Bearer',
        });
        return { data: response.data };
      }

      return {
        error: {
          message: response.error || 'Registration failed',
        },
      };
    } catch (error) {
      return {
        error: {
          message: 'Network error during registration',
        },
      };
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(request: SignInRequest): Promise<{ data?: AuthResponse; error?: AuthError }> {
    try {
      console.log('🌐 Making login API request...');
      const response = await api.post<AuthResponse>('/api/auth/login', request);
      console.log('📡 Login API response:', response);

      if (response.success && response.data) {
        console.log('💾 Storing tokens...');
        // Store tokens
        tokenManager.setTokens({
          access_token: response.data.access_token,
          refresh_token: response.data.refresh_token,
          expires_in: response.data.expires_in,
          token_type: 'Bearer',
        });

        // Verify tokens were stored
        console.log('🔑 Tokens stored, access token:', tokenManager.getAccessToken()?.substring(0, 20) + '...');

        return { data: response.data };
      }

      console.error('❌ Login API failed:', response);
      return {
        error: {
          message: response.error || 'Login failed',
        },
      };
    } catch (error) {
      console.error('❌ Login network error:', error);
      return {
        error: {
          message: 'Network error during login',
        },
      };
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<{ error?: AuthError }> {
    try {
      // Call logout endpoint
      await api.post('/api/auth/logout');

      // Clear tokens regardless of API response
      tokenManager.clearTokens();

      return {};
    } catch (error) {
      // Clear tokens even if API call fails
      tokenManager.clearTokens();
      return {};
    }
  }

  /**
   * Get current user profile
   */
  async getUser(): Promise<{ user?: User; error?: AuthError }> {
    try {
      if (!tokenManager.hasValidToken()) {
        return {
          error: {
            message: 'No active session',
          },
        };
      }

      const response = await api.get<User>('/api/auth/profile');

      if (response.success && response.data) {
        return { user: response.data };
      }

      return {
        error: {
          message: response.error || 'Failed to get user profile',
        },
      };
    } catch (error) {
      return {
        error: {
          message: 'Network error while fetching profile',
        },
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: UpdateProfileRequest): Promise<{ data?: User; error?: AuthError }> {
    try {
      const response = await api.put<User>('/api/users/profile', updates);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: {
          message: response.error || 'Failed to update profile',
        },
      };
    } catch (error) {
      return {
        error: {
          message: 'Network error while updating profile',
        },
      };
    }
  }

  /**
   * Reset password
   */
  async resetPassword(request: ResetPasswordRequest): Promise<{ error?: AuthError }> {
    try {
      const response = await api.post('/api/auth/reset-password', request);

      if (response.success) {
        return {};
      }

      return {
        error: {
          message: response.error || 'Failed to send reset email',
        },
      };
    } catch (error) {
      return {
        error: {
          message: 'Network error during password reset',
        },
      };
    }
  }

  /**
   * Change password
   */
  async changePassword(request: ChangePasswordRequest): Promise<{ error?: AuthError }> {
    try {
      const response = await api.post('/api/auth/change-password', request);

      if (response.success) {
        return {};
      }

      return {
        error: {
          message: response.error || 'Failed to change password',
        },
      };
    } catch (error) {
      return {
        error: {
          message: 'Network error during password change',
        },
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return tokenManager.hasValidToken();
  }

  /**
   * Get current session info
   */
  getSession(): { access_token?: string; refresh_token?: string } {
    return {
      access_token: tokenManager.getAccessToken() || undefined,
      refresh_token: tokenManager.getRefreshToken() || undefined,
    };
  }
}

// Export singleton instance
export const authService = new AuthService();
