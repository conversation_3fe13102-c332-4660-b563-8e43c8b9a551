import { tokenManager } from './api-client';

// Configuration for the Mastra backend
const MASTRA_API_BASE_URL = 'http://localhost:4111'; // Adjust this to your Mastra backend URL

/**
 * Service for interacting with Mastra agents
 * This service handles authentication and communication with the Mastra backend
 */
export class MastraAgentService {
  private async getAuthToken(): Promise<string> {
    const token = tokenManager.getAccessToken();

    if (!token) {
      throw new Error('No active session found. Please sign in.');
    }

    return token;
  }

  /**
   * Generate a response from the DeepLedger agent
   * The agent will automatically use the current user's context (organization, etc.)
   */
  async generateResponse(message: string): Promise<{
    text?: string;
    object?: any;
    toolCalls?: any[];
  }> {
    try {
      const token = await this.getAuthToken();

      const response = await fetch(`${MASTRA_API_BASE_URL}/api/agents/DeepLedger/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Agent request failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error calling Mastra agent:', error);
      throw error;
    }
  }

  /**
   * Stream a response from the DeepLedger agent
   * This provides real-time streaming of the agent's response
   */
  async streamResponse(
    message: string,
    onChunk: (chunk: string) => void,
    onComplete?: (result: any) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      const token = await this.getAuthToken();

      const response = await fetch(`${MASTRA_API_BASE_URL}/api/agents/DeepLedger/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Agent stream request failed: ${response.status} ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.type === 'text-delta' && parsed.textDelta) {
                onChunk(parsed.textDelta);
              } else if (parsed.type === 'finish' && onComplete) {
                onComplete(parsed);
              }
            } catch (e) {
              // Ignore parsing errors for non-JSON lines
            }
          }
        }
      }
    } catch (error) {
      console.error('Error streaming from Mastra agent:', error);
      if (onError) {
        onError(error as Error);
      } else {
        throw error;
      }
    }
  }

  /**
   * Generate a response with structured output
   * This is useful when you need the agent to return data in a specific format
   */
  async generateStructuredResponse<T = any>(
    message: string,
    schema: any
  ): Promise<{
    object: T;
    text?: string;
    toolCalls?: any[];
  }> {
    try {
      const token = await this.getAuthToken();

      const response = await fetch(`${MASTRA_API_BASE_URL}/api/agents/DeepLedger/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: message,
            },
          ],
          output: schema,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Agent request failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error calling Mastra agent with structured output:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const mastraAgent = new MastraAgentService();

// Export types for better TypeScript support
export interface AgentMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface AgentResponse {
  text?: string;
  object?: any;
  toolCalls?: any[];
}
