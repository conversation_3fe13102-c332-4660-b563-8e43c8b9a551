import { api } from './api-client';
import type { ApiResponse } from './api-client';
import type { User } from './auth-service';

// Types for user-related requests
export interface UserExistsRequest {
  email: string;
}

export interface UserExistsResponse {
  exists: boolean;
}

export interface UserStatistics {
  total_users: number;
  active_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
}

export interface PaginatedUsers {
  users: User[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
}

// User service class
export class UserService {
  /**
   * Get user profile
   */
  async getProfile(): Promise<{ data?: User; error?: string }> {
    try {
      const response = await api.get<User>('/api/users/profile');

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'Failed to get user profile',
      };
    } catch (error) {
      return {
        error: 'Network error while fetching profile',
      };
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<User>): Promise<{ data?: User; error?: string }> {
    try {
      const response = await api.put<User>('/api/users/profile', updates);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'Failed to update profile',
      };
    } catch (error) {
      return {
        error: 'Network error while updating profile',
      };
    }
  }

  /**
   * Delete user account
   */
  async deleteAccount(): Promise<{ error?: string }> {
    try {
      const response = await api.delete('/api/users/account');

      if (response.success) {
        return {};
      }

      return {
        error: response.error || 'Failed to delete account',
      };
    } catch (error) {
      return {
        error: 'Network error while deleting account',
      };
    }
  }

  /**
   * Check if user exists by email
   */
  async checkUserExists(email: string): Promise<{ data?: UserExistsResponse; error?: string }> {
    try {
      const response = await api.get<UserExistsResponse>(`/api/users/exists?email=${encodeURIComponent(email)}`);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'Failed to check user existence',
      };
    } catch (error) {
      return {
        error: 'Network error while checking user existence',
      };
    }
  }

  // Admin-only methods
  /**
   * Get all users with pagination (Admin only)
   */
  async getUsers(params: GetUsersParams = {}): Promise<{ data?: PaginatedUsers; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);

      const url = `/api/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await api.get<PaginatedUsers>(url);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'Failed to get users',
      };
    } catch (error) {
      return {
        error: 'Network error while fetching users',
      };
    }
  }

  /**
   * Search user by email (Admin only)
   */
  async searchUserByEmail(email: string): Promise<{ data?: User; error?: string }> {
    try {
      const response = await api.get<User>(`/api/users/search?email=${encodeURIComponent(email)}`);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'User not found',
      };
    } catch (error) {
      return {
        error: 'Network error while searching user',
      };
    }
  }

  /**
   * Get user statistics (Admin only)
   */
  async getUserStatistics(): Promise<{ data?: UserStatistics; error?: string }> {
    try {
      const response = await api.get<UserStatistics>('/api/users/statistics');

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'Failed to get user statistics',
      };
    } catch (error) {
      return {
        error: 'Network error while fetching statistics',
      };
    }
  }

  /**
   * Get user by ID (Admin only)
   */
  async getUserById(id: string): Promise<{ data?: User; error?: string }> {
    try {
      const response = await api.get<User>(`/api/users/${id}`);

      if (response.success && response.data) {
        return { data: response.data };
      }

      return {
        error: response.error || 'User not found',
      };
    } catch (error) {
      return {
        error: 'Network error while fetching user',
      };
    }
  }

  /**
   * Delete user by ID (Admin only)
   */
  async deleteUserById(id: string): Promise<{ error?: string }> {
    try {
      const response = await api.delete(`/api/users/${id}`);

      if (response.success) {
        return {};
      }

      return {
        error: response.error || 'Failed to delete user',
      };
    } catch (error) {
      return {
        error: 'Network error while deleting user',
      };
    }
  }
}

// Export singleton instance
export const userService = new UserService();
