import { useAuthStore } from '../stores/auth-store';

// Types for Mock Memory API (compatible with Mastra Memory API)
export interface Thread {
  id: string;
  resourceId: string;
  title?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, unknown>;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  threadId: string;
}

export interface CreateThreadRequest {
  resourceId: string;
  title?: string;
  metadata?: Record<string, unknown>;
}

export interface GetMessagesRequest {
  threadId: string;
  resourceId?: string;
  selectBy?: {
    last?: number;
    vectorSearchString?: string;
  };
}

class MockMemoryApiService {
  private mockThreads: Thread[] = [];
  private mockMessages: { [threadId: string]: Message[] } = {};

  constructor() {
    console.log('🔧 Mock Memory API initialized for development');
    this.initializeMockData();
  }

  private initializeMockData() {
    // Create some mock threads
    this.mockThreads = [
      {
        id: 'thread_1',
        resourceId: 'user_mock',
        title: 'Financial Planning Discussion',
        createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updatedAt: new Date(Date.now() - 86400000).toISOString(),
        metadata: {},
      },
      {
        id: 'thread_2',
        resourceId: 'user_mock',
        title: 'Budget Analysis',
        createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        updatedAt: new Date(Date.now() - 172800000).toISOString(),
        metadata: {},
      },
    ];

    // Create some mock messages
    this.mockMessages['thread_1'] = [
      {
        id: 'msg_1',
        role: 'user',
        content: 'Hello, can you help me with my financial planning?',
        timestamp: new Date(Date.now() - 86400000),
        threadId: 'thread_1',
      },
      {
        id: 'msg_2',
        role: 'assistant',
        content: 'Hello! I\'d be happy to help you with your financial planning. This is a mock response for testing the interface.',
        timestamp: new Date(Date.now() - 86300000),
        threadId: 'thread_1',
      },
    ];

    this.mockMessages['thread_2'] = [
      {
        id: 'msg_3',
        role: 'user',
        content: 'Can you analyze my budget?',
        timestamp: new Date(Date.now() - 172800000),
        threadId: 'thread_2',
      },
      {
        id: 'msg_4',
        role: 'assistant',
        content: 'I can help you analyze your budget. This is a mock response showing that the memory system is working.',
        timestamp: new Date(Date.now() - 172700000),
        threadId: 'thread_2',
      },
    ];
  }

  private async simulateDelay(ms: number = 300): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get all threads for a user (resource) - Mock implementation
  async getThreads(resourceId: string): Promise<Thread[]> {
    console.log('📋 Mock: Getting threads for resource:', resourceId);
    await this.simulateDelay();

    // Return threads for this resource or all mock threads
    return this.mockThreads.filter(thread =>
      thread.resourceId === resourceId || resourceId === 'user_mock'
    );
  }

  // Get a specific thread by ID - Mock implementation
  async getThread(threadId: string): Promise<Thread | null> {
    console.log('📄 Mock: Getting thread:', threadId);
    await this.simulateDelay();

    const thread = this.mockThreads.find(t => t.id === threadId);
    return thread || null;
  }

  // Create a new thread - Mock implementation
  async createThread(data: CreateThreadRequest): Promise<Thread> {
    console.log('🆕 Mock: Creating thread:', data);
    await this.simulateDelay();

    const newThread: Thread = {
      id: `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      resourceId: data.resourceId,
      title: data.title || 'New Conversation',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: data.metadata || {},
    };

    this.mockThreads.unshift(newThread); // Add to beginning
    this.mockMessages[newThread.id] = []; // Initialize empty messages

    return newThread;
  }

  // Update thread - Mock implementation
  async updateThread(threadId: string, updates: Partial<Thread>): Promise<Thread> {
    console.log('✏️ Mock: Updating thread:', threadId, updates);
    await this.simulateDelay();

    const threadIndex = this.mockThreads.findIndex(t => t.id === threadId);
    if (threadIndex === -1) {
      throw new Error(`Thread ${threadId} not found`);
    }

    this.mockThreads[threadIndex] = {
      ...this.mockThreads[threadIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    return this.mockThreads[threadIndex];
  }

  // Delete a thread - Mock implementation
  async deleteThread(threadId: string): Promise<void> {
    console.log('🗑️ Mock: Deleting thread:', threadId);
    await this.simulateDelay();

    this.mockThreads = this.mockThreads.filter(t => t.id !== threadId);
    delete this.mockMessages[threadId];
  }

  // Auto-update thread title based on first user message - Mock implementation
  async autoUpdateThreadTitle(threadId: string, userMessage: string): Promise<{ updated: boolean; thread?: Thread; message?: string }> {
    console.log('🏷️ Mock: Auto-updating thread title:', threadId, userMessage);
    await this.simulateDelay();

    const thread = this.mockThreads.find(t => t.id === threadId);
    if (!thread) {
      return { updated: false, message: 'Thread not found' };
    }

    // Generate a mock title based on the message
    const title = userMessage.length > 30
      ? userMessage.substring(0, 30) + '...'
      : userMessage;

    const updatedThread = await this.updateThread(threadId, { title });

    return {
      updated: true,
      thread: updatedThread,
      message: 'Title updated successfully'
    };
  }

  // Get messages from a thread - Mock implementation
  async getMessages(request: GetMessagesRequest): Promise<{ messages: Message[]; uiMessages: Message[] }> {
    console.log('💬 Mock: Getting messages for thread:', request.threadId);
    await this.simulateDelay();

    const messages = this.mockMessages[request.threadId] || [];

    // Apply last filter if specified
    let filteredMessages = messages;
    if (request.selectBy?.last) {
      filteredMessages = messages.slice(-request.selectBy.last);
    }

    // For mock, messages and uiMessages are the same
    return {
      messages: filteredMessages,
      uiMessages: filteredMessages,
    };
  }

  // Add a message to a thread (helper for testing)
  async addMessage(threadId: string, message: Omit<Message, 'id' | 'timestamp' | 'threadId'>): Promise<Message> {
    console.log('➕ Mock: Adding message to thread:', threadId);
    await this.simulateDelay(100);

    const newMessage: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...message,
      timestamp: new Date(),
      threadId,
    };

    if (!this.mockMessages[threadId]) {
      this.mockMessages[threadId] = [];
    }

    this.mockMessages[threadId].push(newMessage);

    return newMessage;
  }

  // Check if memory is available - Mock implementation
  async getMemoryStatus(): Promise<{ result: boolean }> {
    console.log('🔍 Mock: Checking memory status');
    await this.simulateDelay(100);
    return { result: true }; // Always available in mock mode
  }
}

export const memoryApi = new MockMemoryApiService();
