/**
 * Mock Mastra AI Client for DeepLedger
 *
 * This service provides a mock implementation for development when the backend is not available.
 * It simulates the Mastra client functionality with placeholder responses.
 */

// Mock implementation - no actual Mastra client import needed

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export interface ToolCall {
  id: string;
  name: string;
  args: Record<string, any>;
  status: 'running' | 'completed' | 'error';
  result?: any;
  error?: string;
  timestamp: Date;
}

export interface MastraResponse {
  message?: string;
  toolCalls?: ToolCall[];
  error?: string;
  isComplete?: boolean;
}

export interface SendMessageOptions {
  threadId?: string;
  resourceId?: string;
  context?: any[];
}

class MastraClient {
  private authToken: string | null = null;
  private isConnected: boolean = false;

  constructor(baseUrl: string = 'mock://localhost:4111') {
    // Mock implementation - no actual client needed
    console.log('🔧 Mock Mastra Client initialized for development');
    this.isConnected = true;
  }

  /**
   * Set authentication token for requests
   */
  setAuthToken(token: string | null) {
    this.authToken = token;
    console.log('🔑 Mock: Auth token set', token ? 'Token provided' : 'Token cleared');
  }

  /**
   * Send a message to the Mastra agent (Mock implementation)
   */
  async sendMessage(message: string, options?: SendMessageOptions): Promise<MastraResponse> {
    console.log('💬 Mock: Sending message:', message);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock response based on message content
    const mockResponses = [
      "I'm a mock AI assistant. The backend is currently not connected, but I can help you test the interface!",
      "This is a placeholder response. Your authentication is working, and you can navigate the dashboard.",
      "Mock response: I understand you're testing the system. Everything looks good on the frontend!",
      "Backend connection is simulated. Your message was: " + message,
    ];

    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];

    return {
      message: randomResponse,
      isComplete: true,
    };
  }

  /**
   * Send a message with streaming response (Mock implementation)
   */
  async sendMessageStream(
    message: string,
    options?: SendMessageOptions,
    onChunk?: (chunk: string) => void,
    onToolCall?: (toolCall: ToolCall) => void,
    onComplete?: () => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    console.log('🌊 Mock: Streaming message:', message);

    try {
      const mockResponse = `Mock streaming response for: "${message}". This is a simulated response to test the streaming functionality without a backend connection.`;

      // Simulate streaming by sending chunks
      const words = mockResponse.split(' ');
      for (let i = 0; i < words.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate delay
        onChunk?.(words[i] + ' ');
      }

      // Simulate a tool call occasionally
      if (Math.random() > 0.7) {
        onToolCall?.({
          id: `mock_tool_${Date.now()}`,
          name: 'mock_calculator',
          args: { operation: 'add', a: 2, b: 3 },
          status: 'running',
          timestamp: new Date(),
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        onToolCall?.({
          id: `mock_tool_${Date.now()}`,
          name: 'mock_calculator',
          args: { operation: 'add', a: 2, b: 3 },
          status: 'completed',
          result: 5,
          timestamp: new Date(),
        });
      }

      onComplete?.();
    } catch (error) {
      console.error('Mock streaming error:', error);
      onError?.(error as Error);
    }
  }

  /**
   * Test connection to the backend (Mock implementation)
   */
  async testConnection(): Promise<boolean> {
    console.log('🔍 Mock: Testing connection...');
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
    console.log('✅ Mock: Connection test successful');
    return true; // Always return true for mock
  }

  /**
   * Get chat history for a thread (Mock implementation)
   */
  async getChatHistory(threadId: string, agentId: string = 'deepLedgerAgent'): Promise<ChatMessage[]> {
    console.log('📜 Mock: Getting chat history for thread:', threadId);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay

    // Return mock chat history
    return [
      {
        id: 'msg_1',
        role: 'user',
        content: 'Hello, can you help me with my finances?',
        timestamp: new Date(Date.now() - 3600000), // 1 hour ago
      },
      {
        id: 'msg_2',
        role: 'assistant',
        content: 'Hello! I\'d be happy to help you with your financial questions. This is a mock response for testing.',
        timestamp: new Date(Date.now() - 3500000),
      },
    ];
  }

  /**
   * Create a new chat thread (Mock implementation)
   */
  async createThread(title?: string, agentId: string = 'deepLedgerAgent'): Promise<{ threadId: string; resourceId: string }> {
    console.log('🆕 Mock: Creating new thread:', title);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay

    const threadId = `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const resourceId = `user_${Date.now()}`;

    return {
      threadId,
      resourceId,
    };
  }

  /**
   * Get all threads for a resource (Mock implementation)
   */
  async getThreads(resourceId: string, agentId: string = 'deepLedgerAgent'): Promise<any[]> {
    console.log('📋 Mock: Getting threads for resource:', resourceId);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate delay

    // Return mock threads
    return [
      {
        id: 'thread_1',
        title: 'Financial Planning Discussion',
        createdAt: new Date(Date.now() - 86400000), // 1 day ago
        resourceId,
        agentId,
      },
      {
        id: 'thread_2',
        title: 'Budget Analysis',
        createdAt: new Date(Date.now() - 172800000), // 2 days ago
        resourceId,
        agentId,
      },
    ];
  }

  /**
   * Update the base URL for the client (Mock implementation)
   */
  updateBaseUrl(baseUrl: string) {
    console.log('🔄 Mock: Base URL updated to:', baseUrl);
    // No actual client to update in mock mode
  }

  /**
   * Generate a unique ID
   */
  generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const mastraClient = new MastraClient();
export default mastraClient;
