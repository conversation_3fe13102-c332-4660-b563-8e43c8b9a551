# Mastra-Style Layout Implementation

This document describes the new Mastra-style layout implementation for the DeepLedger application.

## Overview

The application has been restructured to match the Mastra interface design with:

1. **Left Sidebar**: Collapsible navigation with organization switcher
2. **Top Header**: Simple breadcrumb navigation
3. **Main Content**: Chat-like conversation list or content pages
4. **Right Sidebar**: Collapsible tools and options panel

## New Components

### 1. MastraLayout (`/components/layout/MastraLayout.tsx`)
- Main layout container
- Manages sidebar collapse states
- Routes content based on current view
- Responsive design with smooth transitions

### 2. LeftSidebar (`/components/layout/LeftSidebar.tsx`)
- Collapsible navigation menu
- Organization switcher integration
- Categorized navigation sections:
  - Agents (AI Assistant, Dashboard)
  - Networks
  - Tools (Transactions, Reports, Organization, Settings)
  - MCP Servers
  - Workflows
  - Runtime Context

### 3. ConversationList (`/components/layout/ConversationList.tsx`)
- Chat-like interface for the main content area
- Displays conversation threads
- Integrates with existing chat store
- Shows mock conversations when no real threads exist

## Features

### Responsive Design
- Sidebars collapse/expand with smooth animations
- Content area adjusts automatically
- Mobile-friendly navigation

### Organization Integration
- Organization switcher in left sidebar
- Collapses to icon-only view when sidebar is collapsed

### Chat Integration
- Seamless integration with existing chat system
- Real-time thread loading
- Conversation selection and switching

### Theme Support
- Full dark/light theme support
- Consistent with existing design system

## File Structure

```
frontend/src/components/layout/
├── MastraLayout.tsx          # Main layout component
├── LeftSidebar.tsx           # Left navigation sidebar
└── ConversationList.tsx      # Main content conversation list
```

## Usage

The new layout is automatically used when accessing the dashboard. The `App.tsx` has been updated to use `MastraLayout` instead of `DashboardLayout`.

### Navigation
- Click items in the left sidebar to navigate
- Use the collapse button to minimize the left sidebar
- Use the right sidebar toggle in the header to show/hide tools

### Chat Interface
- Click "New Chat" to start a new conversation
- Click on existing conversations to view them
- The chat interface loads when a conversation is selected

## Testing

To test the new layout:

1. Start the development server:
   ```bash
   cd frontend
   npm run dev
   ```

2. Navigate to the dashboard after logging in

3. Test the following features:
   - Left sidebar collapse/expand
   - Right sidebar toggle
   - Navigation between different views
   - Chat conversation creation and selection
   - Organization switching
   - Theme switching

## Migration Notes

- The old `DashboardLayout` is still available but not used
- All existing functionality is preserved
- No breaking changes to existing components
- Chat system integration is seamless

## Future Enhancements

- Add keyboard shortcuts for navigation
- Implement drag-and-drop for conversation organization
- Add search functionality for conversations
- Enhance right sidebar with contextual tools
- Add mobile-specific optimizations
